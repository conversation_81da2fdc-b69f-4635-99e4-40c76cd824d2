/**
 * @file TradingServer.cpp
 * @brief Implementation of TradingServer class
 * <AUTHOR> Team
 * @date 2024
 */

#include "trading/TradingServer.h"
#include "trading/strategies/FuturesStrategy.h"
#include <iostream>
#include <fstream>
#include <thread>

namespace RoboQuant::Trading {

TradingServer::TradingServer(TradingServerConfig config) : config_(std::move(config)) {
    start_time_ = std::chrono::system_clock::now();
}

TradingServer::~TradingServer() {
    shutdown();
}

std::future<bool> TradingServer::initialize() {
    std::promise<bool> promise;
    auto future = promise.get_future();

    try {
        log_info("Initializing TradingServer: " + config_.server_name);
        set_status(TradingServerStatus::Starting);

        // Initialize components in order
        if (!initialize_components()) {
            log_error("Failed to initialize components");
            set_status(TradingServerStatus::Error);
            promise.set_value(false);
            return future;
        }

        if (!initialize_data_providers()) {
            log_error("Failed to initialize data providers");
            set_status(TradingServerStatus::Error);
            promise.set_value(false);
            return future;
        }

        if (!setup_event_handlers()) {
            log_error("Failed to setup event handlers");
            set_status(TradingServerStatus::Error);
            promise.set_value(false);
            return future;
        }

        if (!setup_timers()) {
            log_error("Failed to setup timers");
            set_status(TradingServerStatus::Error);
            promise.set_value(false);
            return future;
        }

        log_info("TradingServer initialization completed successfully");
        promise.set_value(true);

    } catch (const std::exception& e) {
        log_error("Exception during initialization: " + std::string(e.what()));
        set_status(TradingServerStatus::Error);
        promise.set_value(false);
    }

    return future;
}

std::future<void> TradingServer::start() {
    std::promise<void> promise;
    auto future = promise.get_future();

    try {
        log_info("Starting TradingServer");
        set_status(TradingServerStatus::Starting);

        // Start core components
        event_bus_->start();
        network_manager_->start();

        // Start strategies if auto-start is enabled
        if (config_.auto_start_strategies) {
            auto start_future = start_all_strategies();
            start_future.wait();
        }

        set_status(TradingServerStatus::Running);
        log_info("TradingServer started successfully");

        promise.set_value();

    } catch (const std::exception& e) {
        log_error("Exception during start: " + std::string(e.what()));
        set_status(TradingServerStatus::Error);
        promise.set_exception(std::current_exception());
    }

    return future;
}

std::future<void> TradingServer::stop() {
    std::promise<void> promise;
    auto future = promise.get_future();

    try {
        log_info("Stopping TradingServer");
        set_status(TradingServerStatus::Stopping);

        // Stop strategies first
        auto stop_strategies_future = stop_all_strategies();
        stop_strategies_future.wait();

        // Stop components
        if (network_manager_) {
            network_manager_->stop();
        }

        if (event_bus_) {
            event_bus_->stop();
        }

        set_status(TradingServerStatus::Stopped);
        log_info("TradingServer stopped successfully");

        promise.set_value();

    } catch (const std::exception& e) {
        log_error("Exception during stop: " + std::string(e.what()));
        promise.set_exception(std::current_exception());
    }

    return future;
}

void TradingServer::shutdown() {
    log_info("Shutting down TradingServer");

    if (status_.load() == TradingServerStatus::Running) {
        auto stop_future = stop();
        stop_future.wait();
    }

    // Cleanup components
    strategy_manager_.reset();
    portfolio_manager_.reset();
    order_manager_.reset();
    model_manager_.reset();
    expression_manager_.reset();
    network_manager_.reset();
    event_bus_.reset();
    composite_data_provider_.reset();

    set_status(TradingServerStatus::Stopped);
    log_info("TradingServer shutdown completed");
}

TradingServerStatus TradingServer::get_status() const noexcept {
    return status_.load();
}

bool TradingServer::is_running() const noexcept {
    return status_.load() == TradingServerStatus::Running;
}

std::unordered_map<std::string, std::string> TradingServer::get_health_status() const {
    std::unordered_map<std::string, std::string> health;

    health["server_status"] = std::to_string(static_cast<int>(status_.load()));
    health["uptime_seconds"] = std::to_string(
        std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now() - start_time_
        ).count()
    );

    if (event_bus_) {
        health["event_bus"] = event_bus_->is_running() ? "running" : "stopped";
    }

    if (network_manager_) {
        health["network_manager"] = network_manager_->is_running() ? "running" : "stopped";
    }

    if (strategy_manager_) {
        auto strategies = strategy_manager_->get_all_strategies();
        health["strategy_count"] = std::to_string(strategies.size());

        size_t running_strategies = 0;
        for (const auto* strategy : strategies) {
            if (strategy->get_state() == StrategyState::Running) {
                running_strategies++;
            }
        }
        health["running_strategies"] = std::to_string(running_strategies);
    }

    if (portfolio_manager_) {
        health["portfolio_count"] = std::to_string(portfolio_manager_->portfolio_count());
    }

    if (order_manager_) {
        health["total_orders"] = std::to_string(order_manager_->total_order_count());
        health["active_orders"] = std::to_string(order_manager_->active_order_count());
    }

    if (model_manager_) {
        auto loaded_models = model_manager_->get_loaded_models();
        health["loaded_models"] = std::to_string(loaded_models.size());
    }

    return health;
}

void TradingServer::update_config(const TradingServerConfig& new_config) {
    log_info("Updating server configuration");
    config_ = new_config;

    // Apply configuration changes
    if (network_manager_) {
        network_manager_->set_server_port(config_.server_port);
        network_manager_->set_worker_threads(config_.network_threads);
    }

    // Update global risk limits
    set_global_risk_limits(config_.global_risk_limits);
}

std::future<bool> TradingServer::load_strategy(const StrategyConfig& config) {
    std::promise<bool> promise;
    auto future = promise.get_future();

    try {
        log_info("Loading strategy: " + config.id);

        // Create strategy using registry
        auto& registry = StrategyRegistry::instance();
        auto strategy = registry.create_strategy("FuturesStrategy", config); // Default to FuturesStrategy

        if (!strategy) {
            log_error("Failed to create strategy: " + config.id);
            promise.set_value(false);
            return future;
        }

        // Set up strategy dependencies
        if (portfolio_manager_) {
            // Find or create portfolio for strategy
            auto* portfolio = portfolio_manager_->get_portfolio(config.id + "_portfolio");
            if (portfolio) {
                strategy->set_portfolio(std::shared_ptr<Portfolio>(portfolio, [](Portfolio*){}));
            }
        }

        if (order_manager_) {
            strategy->set_order_manager(std::shared_ptr<OrderManager>(order_manager_.get(), [](OrderManager*){}));
        }

        if (composite_data_provider_) {
            strategy->set_data_provider(std::shared_ptr<DataProvider>(composite_data_provider_.get(), [](DataProvider*){}));
        }

        // Initialize strategy
        auto init_future = strategy->initialize();
        if (!init_future.get()) {
            log_error("Failed to initialize strategy: " + config.id);
            promise.set_value(false);
            return future;
        }

        // Add to strategy manager
        auto add_future = strategy_manager_->add_strategy(std::move(strategy));
        bool success = add_future.get();

        if (success) {
            log_info("Strategy loaded successfully: " + config.id);
        } else {
            log_error("Failed to add strategy to manager: " + config.id);
        }

        promise.set_value(success);

    } catch (const std::exception& e) {
        log_error("Exception loading strategy " + config.id + ": " + e.what());
        promise.set_value(false);
    }

    return future;
}

std::future<bool> TradingServer::unload_strategy(const StrategyId& strategy_id) {
    std::promise<bool> promise;
    auto future = promise.get_future();

    try {
        log_info("Unloading strategy: " + strategy_id);

        auto remove_future = strategy_manager_->remove_strategy(strategy_id);
        bool success = remove_future.get();

        if (success) {
            log_info("Strategy unloaded successfully: " + strategy_id);
        } else {
            log_warning("Strategy not found or failed to unload: " + strategy_id);
        }

        promise.set_value(success);

    } catch (const std::exception& e) {
        log_error("Exception unloading strategy " + strategy_id + ": " + e.what());
        promise.set_value(false);
    }

    return future;
}

std::future<void> TradingServer::start_all_strategies() {
    log_info("Starting all strategies");
    return strategy_manager_->start_all();
}

std::future<void> TradingServer::stop_all_strategies() {
    log_info("Stopping all strategies");
    return strategy_manager_->stop_all();
}

std::future<bool> TradingServer::create_portfolio(const PortfolioConfig& config) {
    std::promise<bool> promise;
    auto future = promise.get_future();

    try {
        log_info("Creating portfolio: " + config.id);

        auto portfolio = std::make_unique<Portfolio>(config);
        bool success = portfolio_manager_->add_portfolio(std::move(portfolio));

        if (success) {
            log_info("Portfolio created successfully: " + config.id);
        } else {
            log_error("Failed to create portfolio: " + config.id);
        }

        promise.set_value(success);

    } catch (const std::exception& e) {
        log_error("Exception creating portfolio " + config.id + ": " + e.what());
        promise.set_value(false);
    }

    return future;
}

std::future<bool> TradingServer::remove_portfolio(const PortfolioId& portfolio_id) {
    std::promise<bool> promise;
    auto future = promise.get_future();

    try {
        log_info("Removing portfolio: " + portfolio_id);

        bool success = portfolio_manager_->remove_portfolio(portfolio_id);

        if (success) {
            log_info("Portfolio removed successfully: " + portfolio_id);
        } else {
            log_warning("Portfolio not found: " + portfolio_id);
        }

        promise.set_value(success);

    } catch (const std::exception& e) {
        log_error("Exception removing portfolio " + portfolio_id + ": " + e.what());
        promise.set_value(false);
    }

    return future;
}

std::vector<PortfolioId> TradingServer::get_portfolio_ids() const {
    return portfolio_manager_->get_portfolio_ids();
}

std::future<bool> TradingServer::load_model(const ModelConfig& config) {
    std::promise<bool> promise;
    auto future = promise.get_future();

    try {
        log_info("Loading model: " + config.id);

        auto load_future = model_manager_->load_model(config);
        bool success = load_future.get();

        if (success) {
            log_info("Model loaded successfully: " + config.id);
        } else {
            log_error("Failed to load model: " + config.id);
        }

        promise.set_value(success);

    } catch (const std::exception& e) {
        log_error("Exception loading model " + config.id + ": " + e.what());
        promise.set_value(false);
    }

    return future;
}

std::future<bool> TradingServer::unload_model(const std::string& model_id) {
    std::promise<bool> promise;
    auto future = promise.get_future();

    try {
        log_info("Unloading model: " + model_id);

        auto unload_future = model_manager_->unload_model(model_id);
        bool success = unload_future.get();

        if (success) {
            log_info("Model unloaded successfully: " + model_id);
        } else {
            log_warning("Model not found: " + model_id);
        }

        promise.set_value(success);

    } catch (const std::exception& e) {
        log_error("Exception unloading model " + model_id + ": " + e.what());
        promise.set_value(false);
    }

    return future;
}

std::vector<std::string> TradingServer::get_loaded_models() const {
    return model_manager_->get_loaded_models();
}

void TradingServer::set_data_provider(std::unique_ptr<DataProvider> provider) {
    if (!composite_data_provider_) {
        composite_data_provider_ = std::make_unique<CompositeDataProvider>();
    }
    composite_data_provider_->add_market_data_provider(std::move(provider));
}

void TradingServer::set_factor_data_provider(std::unique_ptr<FactorDataProvider> provider) {
    if (!composite_data_provider_) {
        composite_data_provider_ = std::make_unique<CompositeDataProvider>();
    }
    composite_data_provider_->add_factor_data_provider(std::move(provider));
}

void TradingServer::set_fundamental_data_provider(std::unique_ptr<FundamentalDataProvider> provider) {
    if (!composite_data_provider_) {
        composite_data_provider_ = std::make_unique<CompositeDataProvider>();
    }
    composite_data_provider_->add_fundamental_data_provider(std::move(provider));
}

void TradingServer::set_global_risk_limits(const RiskLimits& limits) {
    std::unique_lock lock(global_risk_mutex_);
    global_risk_limits_ = limits;

    if (portfolio_manager_) {
        portfolio_manager_->set_global_risk_limits(limits);
    }
}

const RiskLimits& TradingServer::get_global_risk_limits() const {
    std::shared_lock lock(global_risk_mutex_);
    return global_risk_limits_;
}

bool TradingServer::check_global_risk(const OrderRequest& request) const {
    std::shared_lock lock(global_risk_mutex_);

    if (portfolio_manager_) {
        Price current_price = request.price.value_or(100.0); // Default price if not provided
        return portfolio_manager_->check_global_risk_limits(request, current_price);
    }

    return true;
}

PerformanceMetrics TradingServer::get_overall_performance() const {
    std::lock_guard lock(performance_mutex_);
    return overall_performance_;
}

std::unordered_map<StrategyId, PerformanceMetrics> TradingServer::get_strategy_performance() const {
    std::unordered_map<StrategyId, PerformanceMetrics> result;

    if (strategy_manager_) {
        auto strategies = strategy_manager_->get_all_strategies();
        for (const auto* strategy : strategies) {
            result[strategy->get_config().id] = strategy->get_performance();
        }
    }

    return result;
}

std::unordered_map<PortfolioId, PerformanceMetrics> TradingServer::get_portfolio_performance() const {
    std::unordered_map<PortfolioId, PerformanceMetrics> result;

    if (portfolio_manager_) {
        auto portfolios = portfolio_manager_->get_all_portfolios();
        for (const auto* portfolio : portfolios) {
            auto price_provider = [](const AssetId&) -> Price { return 100.0; }; // Placeholder
            result[portfolio->get_id()] = portfolio->calculate_performance(price_provider);
        }
    }

    return result;
}

void TradingServer::set_server_event_callback(ServerEventCallback callback) {
    std::lock_guard lock(callback_mutex_);
    server_event_callback_ = std::move(callback);
}

std::future<std::string> TradingServer::handle_api_request(const std::string& endpoint,
                                                          const std::string& method,
                                                          const std::string& body) {
    std::promise<std::string> promise;
    auto future = promise.get_future();

    try {
        std::string response;

        if (endpoint == "/status") {
            response = handle_status_request();
        } else if (endpoint.starts_with("/strategies")) {
            response = handle_strategies_request(method, body);
        } else if (endpoint.starts_with("/portfolios")) {
            response = handle_portfolios_request(method, body);
        } else if (endpoint.starts_with("/orders")) {
            response = handle_orders_request(method, body);
        } else if (endpoint.starts_with("/models")) {
            response = handle_models_request(method, body);
        } else {
            response = R"({"error":"Unknown endpoint","endpoint":")" + endpoint + R"("})";
        }

        promise.set_value(response);

    } catch (const std::exception& e) {
        std::string error_response = R"({"error":")" + std::string(e.what()) + R"("})";
        promise.set_value(error_response);
    }

    return future;
}

std::future<bool> TradingServer::save_state() {
    std::promise<bool> promise;
    auto future = promise.get_future();

    try {
        log_info("Saving server state");

        // Save portfolios state
        if (portfolio_manager_) {
            auto portfolios = portfolio_manager_->get_all_portfolios();
            for (const auto* portfolio : portfolios) {
                // Save portfolio state to file
                std::string filename = config_.data_directory + "/" + portfolio->get_id() + "_state.json";
                std::ofstream file(filename);
                if (file.is_open()) {
                    file << portfolio->to_json();
                    file.close();
                }
            }
        }

        // Save strategies state
        if (strategy_manager_) {
            auto strategies = strategy_manager_->get_all_strategies();
            for (const auto* strategy : strategies) {
                // Save strategy state to file
                std::string filename = config_.data_directory + "/" + strategy->get_config().id + "_state.json";
                std::ofstream file(filename);
                if (file.is_open()) {
                    file << "{}"; // Placeholder JSON
                    file.close();
                }
            }
        }

        log_info("Server state saved successfully");
        promise.set_value(true);

    } catch (const std::exception& e) {
        log_error("Failed to save server state: " + std::string(e.what()));
        promise.set_value(false);
    }

    return future;
}

std::future<bool> TradingServer::load_state() {
    std::promise<bool> promise;
    auto future = promise.get_future();

    try {
        log_info("Loading server state");

        // Load state files from data directory
        // This is a placeholder implementation

        log_info("Server state loaded successfully");
        promise.set_value(true);

    } catch (const std::exception& e) {
        log_error("Failed to load server state: " + std::string(e.what()));
        promise.set_value(false);
    }

    return future;
}

void TradingServer::enable_auto_save(bool enable, Duration interval) {
    auto_save_enabled_.store(enable);
    auto_save_interval_ = interval;

    if (enable) {
        // Start auto-save timer
        active_timers_.push_back("auto_save");
    }
}

// Private implementation methods
bool TradingServer::initialize_components() {
    try {
        // Initialize event bus
        event_bus_ = std::make_unique<EventBus>();

        // Initialize managers
        strategy_manager_ = std::make_unique<StrategyManager>();
        portfolio_manager_ = std::make_unique<PortfolioManager>();
        order_manager_ = std::make_unique<OrderManager>(nullptr, nullptr); // Will set executors later
        model_manager_ = std::make_unique<ModelManager>();
        expression_manager_ = std::make_unique<ExpressionManager>();
        network_manager_ = std::make_unique<NetworkManager>();

        // Configure network manager
        network_manager_->set_server_port(config_.server_port);
        network_manager_->set_worker_threads(config_.network_threads);

        return true;

    } catch (const std::exception& e) {
        log_error("Failed to initialize components: " + std::string(e.what()));
        return false;
    }
}

bool TradingServer::initialize_data_providers() {
    try {
        // Initialize composite data provider
        composite_data_provider_ = std::make_unique<CompositeDataProvider>();

        // Add default data providers based on configuration
        // This would be expanded based on actual data source configurations

        return true;

    } catch (const std::exception& e) {
        log_error("Failed to initialize data providers: " + std::string(e.what()));
        return false;
    }
}

bool TradingServer::initialize_strategies() {
    // Strategies are loaded separately via load_strategy()
    return true;
}

bool TradingServer::initialize_portfolios() {
    // Portfolios are created separately via create_portfolio()
    return true;
}

bool TradingServer::initialize_models() {
    // Models are loaded separately via load_model()
    return true;
}

bool TradingServer::setup_event_handlers() {
    try {
        // Set up event handlers for market data, orders, etc.
        // This would involve subscribing to various events

        return true;

    } catch (const std::exception& e) {
        log_error("Failed to setup event handlers: " + std::string(e.what()));
        return false;
    }
}

bool TradingServer::setup_timers() {
    try {
        // Setup various timers for heartbeat, risk checks, etc.
        active_timers_.push_back("heartbeat");
        active_timers_.push_back("risk_check");
        active_timers_.push_back("performance_report");

        return true;

    } catch (const std::exception& e) {
        log_error("Failed to setup timers: " + std::string(e.what()));
        return false;
    }
}

// Event handlers
void TradingServer::on_market_data(const QuoteData& quote) {
    if (strategy_manager_) {
        strategy_manager_->distribute_market_data(quote);
    }
}

void TradingServer::on_bar_data(const BarData& bar) {
    if (strategy_manager_) {
        strategy_manager_->distribute_bar_data(bar);
    }
}

void TradingServer::on_order_fill(const OrderFill& fill) {
    if (strategy_manager_) {
        strategy_manager_->distribute_order_fill(fill);
    }

    // Update portfolio
    if (portfolio_manager_) {
        auto* portfolio = portfolio_manager_->get_portfolio(fill.portfolio_id);
        if (portfolio) {
            portfolio->update_position(fill.asset_id,
                                     fill.side == Side::Buy ? fill.quantity : -fill.quantity,
                                     fill.price, fill.commission);
        }
    }
}

void TradingServer::on_order_update(const Order& order) {
    if (strategy_manager_) {
        strategy_manager_->distribute_order_update(order);
    }
}

void TradingServer::on_strategy_event(const StrategyId& strategy_id, const std::string& event) {
    log_info("Strategy event: " + strategy_id + " - " + event);
    notify_server_event("strategy_event", strategy_id + ":" + event);
}

void TradingServer::on_portfolio_event(const PortfolioId& portfolio_id, const std::string& event) {
    log_info("Portfolio event: " + portfolio_id + " - " + event);
    notify_server_event("portfolio_event", portfolio_id + ":" + event);
}

// Timer handlers
void TradingServer::on_heartbeat_timer() {
    // Send heartbeat to monitoring systems
    notify_server_event("heartbeat", "alive");
}

void TradingServer::on_risk_check_timer() {
    // Perform periodic risk checks
    if (portfolio_manager_) {
        auto portfolios = portfolio_manager_->get_all_portfolios();
        for (const auto* portfolio : portfolios) {
            // Check portfolio risk limits
            // This would involve more sophisticated risk calculations
        }
    }
}

void TradingServer::on_performance_report_timer() {
    // Generate performance reports
    auto overall_perf = get_overall_performance();
    log_info("Performance report - Total return: " + std::to_string(overall_perf.total_return));
}

void TradingServer::on_auto_save_timer() {
    if (auto_save_enabled_.load()) {
        auto save_future = save_state();
        // Don't wait for completion to avoid blocking
    }
}

// API handlers
std::string TradingServer::handle_status_request() {
    auto health = get_health_status();

    std::ostringstream oss;
    oss << "{\"status\":\"" << static_cast<int>(status_.load()) << "\",\"health\":{";

    bool first = true;
    for (const auto& [key, value] : health) {
        if (!first) oss << ",";
        oss << "\"" << key << "\":\"" << value << "\"";
        first = false;
    }

    oss << "}}";
    return oss.str();
}

std::string TradingServer::handle_strategies_request(const std::string& method, const std::string& body) {
    if (method == "GET") {
        // Return list of strategies
        std::ostringstream oss;
        oss << "{\"strategies\":[";

        if (strategy_manager_) {
            auto strategies = strategy_manager_->get_all_strategies();
            bool first = true;
            for (const auto* strategy : strategies) {
                if (!first) oss << ",";
                oss << "{\"id\":\"" << strategy->get_config().id << "\","
                    << "\"name\":\"" << strategy->get_config().name << "\","
                    << "\"state\":" << static_cast<int>(strategy->get_state()) << "}";
                first = false;
            }
        }

        oss << "]}";
        return oss.str();
    }

    return R"({"error":"Method not supported"})";
}

std::string TradingServer::handle_portfolios_request(const std::string& method, const std::string& body) {
    if (method == "GET") {
        // Return list of portfolios
        std::ostringstream oss;
        oss << "{\"portfolios\":[";

        if (portfolio_manager_) {
            auto portfolio_ids = portfolio_manager_->get_portfolio_ids();
            bool first = true;
            for (const auto& id : portfolio_ids) {
                if (!first) oss << ",";
                auto* portfolio = portfolio_manager_->get_portfolio(id);
                if (portfolio) {
                    oss << portfolio->to_json();
                }
                first = false;
            }
        }

        oss << "]}";
        return oss.str();
    }

    return R"({"error":"Method not supported"})";
}

std::string TradingServer::handle_orders_request(const std::string& method, const std::string& body) {
    if (method == "GET") {
        // Return list of orders
        std::ostringstream oss;
        oss << "{\"orders\":[";

        if (order_manager_) {
            auto orders = order_manager_->get_all_orders();
            bool first = true;
            for (const auto& order : orders) {
                if (!first) oss << ",";
                oss << order.to_json();
                first = false;
            }
        }

        oss << "]}";
        return oss.str();
    }

    return R"({"error":"Method not supported"})";
}

std::string TradingServer::handle_models_request(const std::string& method, const std::string& body) {
    if (method == "GET") {
        // Return list of models
        std::ostringstream oss;
        oss << "{\"models\":[";

        if (model_manager_) {
            auto models = model_manager_->get_loaded_models();
            bool first = true;
            for (const auto& model_id : models) {
                if (!first) oss << ",";
                oss << "\"" << model_id << "\"";
                first = false;
            }
        }

        oss << "]}";
        return oss.str();
    }

    return R"({"error":"Method not supported"})";
}

// Utility methods
void TradingServer::set_status(TradingServerStatus status) {
    status_.store(status);
    notify_server_event("status_change", std::to_string(static_cast<int>(status)));
}

void TradingServer::log_info(const std::string& message) const {
    std::cout << "[INFO][" << config_.server_id << "] " << message << std::endl;
}

void TradingServer::log_warning(const std::string& message) const {
    std::cout << "[WARN][" << config_.server_id << "] " << message << std::endl;
}

void TradingServer::log_error(const std::string& message) const {
    std::cerr << "[ERROR][" << config_.server_id << "] " << message << std::endl;
}

void TradingServer::notify_server_event(const std::string& event_type, const std::string& data) {
    std::lock_guard lock(callback_mutex_);
    if (server_event_callback_) {
        server_event_callback_(event_type, data);
    }
}

// TradingServerBuilder Implementation
TradingServerBuilder& TradingServerBuilder::with_config(const TradingServerConfig& config) {
    config_ = config;
    return *this;
}

TradingServerBuilder& TradingServerBuilder::with_server_port(uint16_t port) {
    config_.server_port = port;
    return *this;
}

TradingServerBuilder& TradingServerBuilder::with_data_provider(std::unique_ptr<DataProvider> provider) {
    data_provider_ = std::move(provider);
    return *this;
}

TradingServerBuilder& TradingServerBuilder::with_strategy_config(const std::string& config_file) {
    config_.strategy_config_file = config_file;
    return *this;
}

TradingServerBuilder& TradingServerBuilder::with_model_directory(const std::string& directory) {
    config_.model_directory = directory;
    return *this;
}

TradingServerBuilder& TradingServerBuilder::with_risk_limits(const RiskLimits& limits) {
    config_.global_risk_limits = limits;
    return *this;
}

TradingServerBuilder& TradingServerBuilder::with_log_level(const std::string& level) {
    config_.log_level = level;
    return *this;
}

std::unique_ptr<TradingServer> TradingServerBuilder::build() {
    auto server = std::make_unique<TradingServer>(config_);

    if (data_provider_) {
        server->set_data_provider(std::move(data_provider_));
    }

    return server;
}

} // namespace RoboQuant::Trading