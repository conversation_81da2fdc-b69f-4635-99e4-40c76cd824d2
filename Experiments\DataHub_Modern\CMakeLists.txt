cmake_minimum_required(VERSION 3.20)

project(DataHub_Modern 
    VERSION 1.0.0
    DESCRIPTION "Modern C++20 DataHub Implementation"
    LANGUAGES CXX
)

# ???? C++20 ???
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# ???????
if(MSVC)
    add_compile_options(/W4 /utf-8)
    add_compile_options(/std:c++20)
    # ???????????
    # add_compile_options(/await)  # ???????????
    # ???????????
    add_compile_options(/wd4828)  # ???? UTF-8 ???????
else()
    add_compile_options(-Wall -Wextra -Werror -pedantic)
    add_compile_options(-fcoroutines)
endif()

# ????????
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# ???????
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_compile_definitions(DEBUG)
    if(MSVC)
        add_compile_options(/Od /Zi)
    else()
        add_compile_options(-O0 -g)
    endif()
else()
    add_compile_definitions(NDEBUG)
    if(MSVC)
        add_compile_options(/O2)
    else()
        add_compile_options(-O3 -DNDEBUG)
    endif()
endif()

# ??????????
find_package(Threads REQUIRED)

# ???????
option(ENABLE_TESTS "Enable unit tests" OFF)
option(ENABLE_EXAMPLES "Enable examples" OFF)
option(ENABLE_BENCHMARKS "Enable benchmarks" OFF)
option(ENABLE_LIBTORCH "Enable LibTorch support" OFF)

# ????????
include(FetchContent)

# spdlog ?????
FetchContent_Declare(
    spdlog
    GIT_REPOSITORY https://github.com/gabime/spdlog.git
    GIT_TAG v1.12.0
)
FetchContent_MakeAvailable(spdlog)

# nlohmann/json
FetchContent_Declare(
    nlohmann_json
    GIT_REPOSITORY https://github.com/nlohmann/json.git
    GIT_TAG v3.11.2
)
FetchContent_MakeAvailable(nlohmann_json)

# fmt ???????
FetchContent_Declare(
    fmt
    GIT_REPOSITORY https://github.com/fmtlib/fmt.git
    GIT_TAG 10.1.1
)
FetchContent_MakeAvailable(fmt)

# Catch2 ??????
if(ENABLE_TESTS)
    FetchContent_Declare(
        Catch2
        GIT_REPOSITORY https://github.com/catchorg/Catch2.git
        GIT_TAG v3.4.0
    )
    FetchContent_MakeAvailable(Catch2)
endif()

# LibTorch (???)
if(ENABLE_LIBTORCH)
    find_package(Torch REQUIRED)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${TORCH_CXX_FLAGS}")
endif()

# ??????
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# ?????
set(CORE_SOURCES
    src/core/Types.cpp
    src/core/MarketData.cpp
    src/core/SecurityInfo.cpp
)

set(CORE_HEADERS
    include/core/Types.h
    include/core/MarketData.h
    include/core/SecurityInfo.h
)

# Data layer sources
set(DATA_SOURCES
    src/data/SqliteRepository.cpp
    # src/data/LevelDBRepository.cpp  # Disabled due to missing dependencies
    src/data/RepositoryFactory.cpp
)

set(DATA_HEADERS
    include/data/IDataRepository.h
    # include/data/IDataRepository_Simple.h  # Disabled due to duplicate definitions
    include/data/SqliteRepository.h
    # include/data/LevelDBRepository.h  # Disabled due to missing dependencies
    include/data/RepositoryFactory.h
)

# Service layer sources (temporarily disabled due to interface conflicts)
set(SERVICE_SOURCES
    # src/services/QuoteService.cpp  # Disabled due to interface conflicts
    # src/services/HistoryService.cpp  # Disabled due to interface conflicts
    # src/services/SecurityService.cpp  # Disabled due to interface conflicts
    # src/services/IndicatorService.cpp  # Disabled due to interface conflicts
    # src/services/EventBus.cpp  # Disabled due to interface conflicts
    # src/services/ServiceFactory.cpp  # Disabled due to interface conflicts
)

set(SERVICE_HEADERS
    include/services/IDataService.h
    # include/services/QuoteService.h  # Disabled due to interface conflicts
    # include/services/HistoryService.h  # Disabled due to interface conflicts
    # include/services/SecurityService.h  # Disabled due to interface conflicts
    # include/services/IndicatorService.h  # Disabled due to interface conflicts
    # include/services/EventBus.h  # Disabled due to interface conflicts
    # include/services/ServiceFactory.h  # Disabled due to interface conflicts
)

# API layer sources (temporarily disabled due to service layer dependencies)
set(API_SOURCES
    # src/api/DataHubAPI.cpp  # Disabled due to service layer dependencies
    # src/api/RestAPI.cpp  # Disabled due to service layer dependencies
    # src/api/PythonAPI.cpp  # Disabled due to service layer dependencies
)

set(API_HEADERS
    include/api/DataHubAPI.h
    # include/api/RestAPI.h  # Disabled due to service layer dependencies
    # include/api/PythonAPI.h  # Disabled due to service layer dependencies
)

# ?????????
add_library(datahub_core STATIC
    ${CORE_SOURCES}
    ${CORE_HEADERS}
)

target_link_libraries(datahub_core
    PUBLIC
        spdlog::spdlog
        nlohmann_json::nlohmann_json
        fmt::fmt
        Threads::Threads
)

target_include_directories(datahub_core
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
)

# ????????????
add_library(datahub_data STATIC
    ${DATA_SOURCES}
    ${DATA_HEADERS}
)

target_link_libraries(datahub_data
    PUBLIC
        datahub_core
)

# ?????????
add_library(datahub_services STATIC
    ${SERVICE_SOURCES}
    ${SERVICE_HEADERS}
)

target_link_libraries(datahub_services
    PUBLIC
        datahub_core
        datahub_data
)

# ????API??
add_library(datahub_api STATIC
    ${API_SOURCES}
    ${API_HEADERS}
)

target_link_libraries(datahub_api
    PUBLIC
        datahub_services
)

# ????
add_library(datahub STATIC)
target_link_libraries(datahub
    PUBLIC
        datahub_core
        datahub_data
        datahub_services
        datahub_api
)

# LibTorch ???
if(ENABLE_LIBTORCH)
    target_link_libraries(datahub PUBLIC ${TORCH_LIBRARIES})
    target_compile_definitions(datahub PUBLIC ENABLE_LIBTORCH)
endif()

# ???????
install(TARGETS datahub datahub_core datahub_data datahub_services datahub_api
    EXPORT DataHubTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

install(DIRECTORY include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h"
)

# install(EXPORT DataHubTargets
#     FILE DataHubTargets.cmake
#     NAMESPACE DataHub::
#     DESTINATION lib/cmake/DataHub
# )

# ???????
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    DataHubConfigVersion.cmake
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

# configure_package_config_file(
#     ${CMAKE_CURRENT_SOURCE_DIR}/cmake/DataHubConfig.cmake.in
#     ${CMAKE_CURRENT_BINARY_DIR}/DataHubConfig.cmake
#     INSTALL_DESTINATION lib/cmake/DataHub
# )

# install(FILES
#     ${CMAKE_CURRENT_BINARY_DIR}/DataHubConfig.cmake
#     ${CMAKE_CURRENT_BINARY_DIR}/DataHubConfigVersion.cmake
#     DESTINATION lib/cmake/DataHub
# )

# ????
if(ENABLE_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# ???
if(ENABLE_EXAMPLES)
    add_subdirectory(examples)
endif()

# ???????
if(ENABLE_BENCHMARKS)
    add_subdirectory(benchmarks)
endif()

# ???????
find_package(Doxygen)
if(DOXYGEN_FOUND)
    set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/docs/Doxyfile.in)
    set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
    
    configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
    
    add_custom_target(docs
        COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Generating API documentation with Doxygen"
        VERBATIM
    )
endif()

# ???
set(CPACK_PROJECT_NAME ${PROJECT_NAME})
set(CPACK_PROJECT_VERSION ${PROJECT_VERSION})
include(CPack)
