﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\tests\test_core_types.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\tests\test_market_data.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\tests\test_security_info.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\tests\test_sqlite_repository.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\tests\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Natvis Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\nlohmann_json.natvis" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{ED3CB8E5-FD7A-3664-A7D3-A35456B4DB16}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
