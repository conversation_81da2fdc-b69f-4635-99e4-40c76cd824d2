// DataHub API Implementation
#include "api/DataHubAPI.h"
#include <stdexcept>

namespace DataHub::API {

DataHubAPI::DataHubAPI(std::shared_ptr<Services::IDataHubManager> manager)
    : manager_(std::move(manager)) {
    if (!manager_) {
        throw std::invalid_argument("DataHub manager cannot be null");
    }
}

APIResponse<Core::QuoteData> DataHubAPI::get_quote(const std::string& symbol) {
    try {
        // Implementation would call manager's quote service
        // For now, return a placeholder
        Core::QuoteData quote;
        quote.symbol = symbol;
        quote.timestamp = Core::now();
        quote.bid_price = 100.0;
        quote.ask_price = 100.1;
        quote.bid_volume = 1000;
        quote.ask_volume = 1000;
        
        return APIResponse<Core::QuoteData>::success_response(std::move(quote));
    } catch (const std::exception& e) {
        return APIResponse<Core::QuoteData>::error_response(e.what());
    }
}

APIResponse<std::vector<Core::QuoteData>> DataHubAPI::get_quotes(const std::vector<std::string>& symbols) {
    try {
        std::vector<Core::QuoteData> quotes;
        quotes.reserve(symbols.size());
        
        for (const auto& symbol : symbols) {
            auto result = get_quote(symbol);
            if (result.success) {
                quotes.push_back(std::move(result.data));
            }
        }
        
        return APIResponse<std::vector<Core::QuoteData>>::success_response(std::move(quotes));
    } catch (const std::exception& e) {
        return APIResponse<std::vector<Core::QuoteData>>::error_response(e.what());
    }
}

APIResponse<std::vector<Core::BarData>> DataHubAPI::get_bars(
    const std::string& symbol,
    const std::string& bar_size,
    const std::string& start_time,
    const std::string& end_time) {
    
    try {
        // Implementation would call manager's history service
        // For now, return empty vector
        std::vector<Core::BarData> bars;
        return APIResponse<std::vector<Core::BarData>>::success_response(std::move(bars));
    } catch (const std::exception& e) {
        return APIResponse<std::vector<Core::BarData>>::error_response(e.what());
    }
}

APIResponse<Core::SecurityInfo> DataHubAPI::get_security(const std::string& symbol) {
    try {
        // Implementation would call manager's security service
        // For now, return a placeholder
        Core::SecurityInfo security(symbol, symbol, Core::MarketType::Stock, Core::Exchange::SSE);
        return APIResponse<Core::SecurityInfo>::success_response(std::move(security));
    } catch (const std::exception& e) {
        return APIResponse<Core::SecurityInfo>::error_response(e.what());
    }
}

APIResponse<std::vector<Core::SecurityInfo>> DataHubAPI::search_securities(const std::string& query) {
    try {
        // Implementation would call manager's security service
        // For now, return empty vector
        std::vector<Core::SecurityInfo> securities;
        return APIResponse<std::vector<Core::SecurityInfo>>::success_response(std::move(securities));
    } catch (const std::exception& e) {
        return APIResponse<std::vector<Core::SecurityInfo>>::error_response(e.what());
    }
}

APIResponse<bool> DataHubAPI::health_check() {
    try {
        bool is_healthy = manager_ && manager_->is_running();
        return APIResponse<bool>::success_response(is_healthy);
    } catch (const std::exception& e) {
        return APIResponse<bool>::error_response(e.what());
    }
}

} // namespace DataHub::API
