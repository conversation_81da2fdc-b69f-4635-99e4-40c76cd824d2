#include <catch2/catch_test_macros.hpp>
#include "data/SqliteRepository.h"
#include "core/MarketData.h"
#include <filesystem>

using namespace DataHub::Core;
using namespace DataHub::Data;

TEST_CASE("SqliteRepository - Basic functionality", "[sqlite]") {
    // Use a temporary database file
    std::string db_path = "test_datahub.db";

    // Clean up any existing test database
    if (std::filesystem::exists(db_path)) {
        std::filesystem::remove(db_path);
    }

    SqliteRepository repo(db_path);

    SECTION("Connection management") {
        REQUIRE_FALSE(repo.is_connected());

        auto connect_result = repo.connect();
        REQUIRE(connect_result.is_success());
        REQUIRE(repo.is_connected());

        auto health_result = repo.health_check();
        REQUIRE(health_result.is_success());
        REQUIRE(health_result.value() == true);

        auto disconnect_result = repo.disconnect();
        REQUIRE(disconnect_result.is_success());
        REQUIRE_FALSE(repo.is_connected());
    }
    
    SECTION("Quote data operations") {
        auto connect_result = repo.connect();
        REQUIRE(connect_result.is_success());
        
        // Create test quote data
        QuoteData quote;
        quote.symbol = "000001.SZ";
        quote.timestamp = now();
        quote.bid_prices[0] = 10.50;
        quote.ask_prices[0] = 10.52;
        quote.bid_volumes[0] = 1000;
        quote.ask_volumes[0] = 1500;
        quote.last_price = 10.51;
        quote.volume = 2500;
        
        // Save quote
        auto save_result = repo.save_quote(quote);
        REQUIRE(save_result.is_success());
        
        // Retrieve latest quote
        auto get_result = repo.get_latest_quote("000001.SZ");
        REQUIRE(get_result.is_success());
        
        const auto& retrieved_quote = get_result.value();
        REQUIRE(retrieved_quote.symbol == quote.symbol);
        REQUIRE(retrieved_quote.bid_prices[0] == quote.bid_prices[0]);
        REQUIRE(retrieved_quote.ask_prices[0] == quote.ask_prices[0]);
        REQUIRE(retrieved_quote.bid_volumes[0] == quote.bid_volumes[0]);
        REQUIRE(retrieved_quote.ask_volumes[0] == quote.ask_volumes[0]);
        
        repo.disconnect();
    }
    
    SECTION("Bar data operations") {
        auto connect_result = repo.connect();
        REQUIRE(connect_result.is_success());
        
        // Create test bar data
        BarData bar("000001.SZ", now(), BarSize::Minute1, BarType::Candle);
        bar.open = 10.00;
        bar.high = 10.50;
        bar.low = 9.95;
        bar.close = 10.30;
        bar.volume = 1000000;
        bar.amount = 10250000.0;
        
        // Save bar
        auto save_result = repo.save_bar(bar);
        REQUIRE(save_result.is_success());
        
        // Retrieve latest bar
        auto get_result = repo.get_latest_bar("000001.SZ", BarSize::Minute1, BarType::Candle);
        REQUIRE(get_result.is_success());
        
        const auto& retrieved_bar = get_result.value();
        REQUIRE(retrieved_bar.symbol == bar.symbol);
        REQUIRE(retrieved_bar.open == bar.open);
        REQUIRE(retrieved_bar.high == bar.high);
        REQUIRE(retrieved_bar.low == bar.low);
        REQUIRE(retrieved_bar.close == bar.close);
        REQUIRE(retrieved_bar.volume == bar.volume);
        
        repo.disconnect();
    }
    
    // Clean up test database
    if (std::filesystem::exists(db_path)) {
        std::filesystem::remove(db_path);
    }
}

TEST_CASE("SqliteRepository - Batch operations", "[sqlite]") {
    std::string db_path = "test_batch.db";
    
    // Clean up any existing test database
    if (std::filesystem::exists(db_path)) {
        std::filesystem::remove(db_path);
    }
    
    SqliteRepository repo(db_path);
    auto connect_result = repo.connect();
    REQUIRE(connect_result.is_success());
    
    SECTION("Batch quote operations") {
        std::vector<QuoteData> quotes;
        
        for (int i = 0; i < 5; ++i) {
            QuoteData quote;
            quote.symbol = "00000" + std::to_string(i) + ".SZ";
            quote.timestamp = now();
            quote.bid_prices[0] = 10.0 + i * 0.1;
            quote.ask_prices[0] = 10.02 + i * 0.1;
            quote.bid_volumes[0] = 1000 + i * 100;
            quote.ask_volumes[0] = 1500 + i * 100;
            quote.last_price = 10.01 + i * 0.1;
            quote.volume = 2500 + i * 100;
            quotes.push_back(quote);
        }
        
        // Batch save quotes
        auto save_result = repo.save_quotes(quotes);
        REQUIRE(save_result.is_success());
        
        // Verify all quotes were saved
        for (const auto& quote : quotes) {
            auto get_result = repo.get_latest_quote(quote.symbol);
            REQUIRE(get_result.is_success());
            REQUIRE(get_result.value().symbol == quote.symbol);
        }
    }
    
    repo.disconnect();
    
    // Clean up test database
    if (std::filesystem::exists(db_path)) {
        std::filesystem::remove(db_path);
    }
}
