#pragma once

#include "../core/Types.h"
#include "../core/MarketData.h"
#include "../data/IDataRepository.h"
#include "IDataService.h"
#include <memory>
#include <atomic>

namespace DataHub::Services {

// Quote service configuration
struct QuoteServiceConfig {
    std::size_t max_cache_size{10000};
    std::chrono::milliseconds cache_ttl{30000};
    bool enable_real_time{true};
    bool enable_persistence{true};
};

// Quote service interface
class IQuoteService : public virtual IDataService {
public:
    virtual ~IQuoteService() = default;
    
    // Quote operations
    virtual Core::Result<Core::QuoteData> get_latest_quote(const Core::Symbol& symbol) = 0;
    virtual Core::Result<Core::QuoteDataVector> get_quotes(const std::vector<Core::Symbol>& symbols) = 0;
    virtual Core::Result<void> save_quote(const Core::QuoteData& quote) = 0;
    virtual Core::Result<void> save_quotes(Core::QuoteDataSpanConst quotes) = 0;
    
    // Subscription operations
    virtual Core::Result<void> subscribe_quote(const Core::Symbol& symbol) = 0;
    virtual Core::Result<void> unsubscribe_quote(const Core::Symbol& symbol) = 0;
    virtual Core::Result<std::vector<Core::Symbol>> get_subscribed_symbols() = 0;
};

// Quote service implementation
class QuoteService : public IQuoteService {
public:
    explicit QuoteService(
        std::shared_ptr<Data::IQuoteRepository> quote_repo,
        std::shared_ptr<Data::ITickRepository> tick_repo,
        QuoteServiceConfig config = {});
    
    ~QuoteService() override;
    
    // IDataService interface
    Core::Result<void> start() override;
    Core::Result<void> stop() override;
    bool is_running() const noexcept override;
    
    // IQuoteService interface
    Core::Result<Core::QuoteData> get_latest_quote(const Core::Symbol& symbol) override;
    Core::Result<Core::QuoteDataVector> get_quotes(const std::vector<Core::Symbol>& symbols) override;
    Core::Result<void> save_quote(const Core::QuoteData& quote) override;
    Core::Result<void> save_quotes(Core::QuoteDataSpanConst quotes) override;
    
    Core::Result<void> subscribe_quote(const Core::Symbol& symbol) override;
    Core::Result<void> unsubscribe_quote(const Core::Symbol& symbol) override;
    Core::Result<std::vector<Core::Symbol>> get_subscribed_symbols() override;

private:
    QuoteServiceConfig config_;
    std::atomic<bool> running_;
    std::atomic<bool> stop_requested_;
    
    std::shared_ptr<Data::IQuoteRepository> quote_repo_;
    std::shared_ptr<Data::ITickRepository> tick_repo_;
};

} // namespace DataHub::Services
