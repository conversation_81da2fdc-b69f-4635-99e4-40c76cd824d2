#include <catch2/catch_test_macros.hpp>
#include "core/SecurityInfo.h"

using namespace DataHub::Core;

TEST_CASE("SecurityInfo - Basic functionality", "[security]") {
    SECTION("Stock info") {
        SecurityInfo security("000001.SZ", "Ping An Bank", MarketType::Stock, Exchange::SZSE);
        security.tick_size = 0.01;
        security.min_order_qty = 100;

        SecurityInfo::StockInfo stock_info;
        stock_info.industry = "Banking";
        stock_info.sector = "Finance";
        stock_info.total_shares = **********;
        stock_info.float_shares = *********;

        security.set_stock_info(std::move(stock_info));

        REQUIRE(security.is_stock());
        REQUIRE_FALSE(security.is_future());
        REQUIRE(security.is_valid());

        auto* stock = security.get_stock_info();
        REQUIRE(stock != nullptr);
        REQUIRE(stock->industry == "Banking");
        REQUIRE(stock->total_shares == **********);
    }

    SECTION("Future info") {
        SecurityInfo security("IF2312", "CSI 300 Future", MarketType::Future, Exchange::CFFEX);

        SecurityInfo::FutureInfo future_info;
        future_info.underlying = "CSI 300";
        future_info.contract_month = "2312";
        future_info.contract_multiplier = 300.0;
        future_info.margin_ratio = 0.12;
        future_info.is_main_contract = true;

        security.set_future_info(std::move(future_info));

        REQUIRE(security.is_future());
        REQUIRE(security.is_main_contract());

        auto* future = security.get_future_info();
        REQUIRE(future != nullptr);
        REQUIRE(future->underlying == "CSI 300");
        REQUIRE(future->contract_multiplier == 300.0);
    }
}

TEST_CASE("BlockInfo - Block management", "[security]") {
    SECTION("Block operations") {
        BlockInfo block("Banking Sector", BlockInfo::BlockType::Industry);
        block.description = "Banking industry related stocks";

        block.add_symbol("000001.SZ");
        block.add_symbol("600036.SH");
        block.add_symbol("601988.SH");

        REQUIRE(block.size() == 3);
        REQUIRE(block.contains("000001.SZ"));
        REQUIRE(block.contains("600036.SH"));
        REQUIRE_FALSE(block.contains("000002.SZ"));

        block.remove_symbol("600036.SH");
        REQUIRE(block.size() == 2);
        REQUIRE_FALSE(block.contains("600036.SH"));

        REQUIRE(block.is_valid());
    }
}
