#pragma once

#include <chrono>
#include <string>
#include <string_view>
#include <optional>
#include <variant>
#include <span>
#include <vector>
#include <memory>
#include <cstdint>
#include <stdexcept>
#include <concepts>

namespace DataHub::Core {

// Time related type aliases
using TimePoint = std::chrono::system_clock::time_point;
using Duration = std::chrono::milliseconds;
using Timestamp = std::chrono::time_point<std::chrono::system_clock, std::chrono::milliseconds>;

// Market data type aliases
using Price = double;
using Volume = std::uint64_t;
using Amount = double;

// Symbol type aliases
using Symbol = std::string;
using SymbolView = std::string_view;

// Market type enumeration
enum class MarketType : std::uint8_t {
    Stock = 0,
    Future = 1,
    Option = 2,
    Bond = 3,
    Fund = 4,
    Crypto = 5
};

enum class Exchange : std::uint8_t {
    SSE = 0,    // Shanghai Stock Exchange
    SZSE = 1,   // Shenzhen Stock Exchange
    SHFE = 2,   // Shanghai Futures Exchange
    DCE = 3,    // Dalian Commodity Exchange
    CZCE = 4,   // Zhengzhou Commodity Exchange
    CFFEX = 5,  // China Financial Futures Exchange
    INE = 6,    // Shanghai International Energy Exchange
    BSE = 7,    // Beijing Stock Exchange
    Unknown = 255
};

enum class BarSize : std::uint8_t {
    Tick = 0,
    Second1 = 1,
    Second5 = 2,
    Second15 = 3,
    Second30 = 4,
    Minute1 = 5,
    Minute5 = 6,
    Minute15 = 7,
    Minute30 = 8,
    Hour1 = 9,
    Day = 10,
    Week = 11,
    Month = 12,
    Quarter = 13,
    Year = 14,
    Range = 15  // ????????
};

enum class BarType : std::uint8_t {
    Candle = 0,  // K??
    Range = 1,   // ????????
    Volume = 2,  // ??????
    Tick = 3     // ???????
};

enum class DataRunMode : std::uint8_t {
    Active = 0,     // ????
    Simulation = 1, // ?????
    Backtest = 2    // ?????
};

// ??????
enum class ErrorCode : std::uint32_t {
    Success = 0,
    InvalidParameter = 1,
    DataNotFound = 2,
    DatabaseError = 3,
    NetworkError = 4,
    PermissionDenied = 5,
    InternalError = 6
};

// Result type for error handling
template<typename T>
class Result {
public:
    Result(T&& value) : data_(std::move(value)), error_(ErrorCode::Success) {}
    Result(const T& value) : data_(value), error_(ErrorCode::Success) {}
    Result(ErrorCode error, std::string message = "")
        : error_(error), error_message_(std::move(message)) {}

    bool is_success() const noexcept { return error_ == ErrorCode::Success; }
    bool is_error() const noexcept { return error_ != ErrorCode::Success; }

    ErrorCode error() const noexcept { return error_; }
    const std::string& error_message() const noexcept { return error_message_; }

    const T& value() const& {
        if (is_error()) throw std::runtime_error("Accessing value of error result");
        return data_.value();
    }

    T&& value() && {
        if (is_error()) throw std::runtime_error("Accessing value of error result");
        return std::move(data_.value());
    }

private:
    std::optional<T> data_;
    ErrorCode error_;
    std::string error_message_;
};

// ??? void ????
template<>
class Result<void> {
public:
    Result() : error_(ErrorCode::Success) {}
    Result(ErrorCode error, std::string message = "") 
        : error_(error), error_message_(std::move(message)) {}

    bool is_success() const noexcept { return error_ == ErrorCode::Success; }
    bool is_error() const noexcept { return error_ != ErrorCode::Success; }
    
    ErrorCode error() const noexcept { return error_; }
    const std::string& error_message() const noexcept { return error_message_; }

private:
    ErrorCode error_;
    std::string error_message_;
};

// ????????
template<typename T>
Result<T> make_success(T&& value) {
    return Result<T>(std::forward<T>(value));
}

inline Result<void> make_success() {
    return Result<void>();
}

template<typename T = void>
Result<T> make_error(ErrorCode error, std::string message = "") {
    return Result<T>(error, std::move(message));
}

// ????????????
template<typename T>
using DataVector = std::vector<T>;

template<typename T>
using DataSpan = std::span<T>;

template<typename T>
using DataSpanConst = std::span<const T>;

// ???????????
template<typename T>
using UniquePtr = std::unique_ptr<T>;

template<typename T>
using SharedPtr = std::shared_ptr<T>;

template<typename T>
using WeakPtr = std::weak_ptr<T>;

// ????????
template<typename T, typename... Args>
UniquePtr<T> make_unique(Args&&... args) {
    return std::make_unique<T>(std::forward<Args>(args)...);
}

template<typename T, typename... Args>
SharedPtr<T> make_shared(Args&&... args) {
    return std::make_shared<T>(std::forward<Args>(args)...);
}

// ?????? (C++20)
template<typename T>
concept Numeric = std::is_arithmetic_v<T>;

template<typename T>
concept StringLike = std::convertible_to<T, std::string_view>;

// Common constants
namespace Constants {
    constexpr double EPSILON = 1e-9;
    constexpr std::size_t DEFAULT_CAPACITY = 1024;
    constexpr std::size_t MAX_SYMBOL_LENGTH = 32;
    constexpr std::size_t MAX_NAME_LENGTH = 128;
}

// Enum to string conversion functions
std::string to_string(MarketType market_type);
std::string to_string(Exchange exchange);
std::string to_string(BarSize bar_size);
std::string to_string(BarType bar_type);
std::string to_string(DataRunMode run_mode);
std::string to_string(ErrorCode error_code);

std::optional<MarketType> parse_market_type(const std::string& str);
std::optional<Exchange> parse_exchange(const std::string& str);
std::optional<BarSize> parse_bar_size(const std::string& str);
std::optional<BarType> parse_bar_type(const std::string& str);

// Time utility functions
std::string format_timestamp(const Timestamp& timestamp, const std::string& format = "%Y-%m-%d %H:%M:%S");
Timestamp parse_timestamp(const std::string& str, const std::string& format = "%Y-%m-%d %H:%M:%S");
Timestamp now();

// Floating point comparison utility functions
bool is_equal(double a, double b, double epsilon = Constants::EPSILON);
bool is_zero(double value, double epsilon = Constants::EPSILON);
bool is_positive(double value, double epsilon = Constants::EPSILON);
bool is_negative(double value, double epsilon = Constants::EPSILON);

// String utility functions
std::string trim(const std::string& str);
std::vector<std::string> split(const std::string& str, char delimiter);
std::string join(const std::vector<std::string>& strings, const std::string& delimiter);

// Hash utility functions
std::size_t hash_combine(std::size_t seed, std::size_t hash);

} // namespace DataHub::Core
