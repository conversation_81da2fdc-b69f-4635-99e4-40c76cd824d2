// DataHub API - Simplified C++ and Python Interface
#pragma once

#include "../core/Types.h"
#include "../core/MarketData.h"
#include "../core/SecurityInfo.h"
#include "../services/DataHubManager.h"
#include <memory>
#include <string>
#include <vector>
#include <functional>

namespace DataHub::API {

// API response wrapper
template<typename T>
struct APIResponse {
    bool success{false};
    std::string message;
    T data{};
    std::string error_code;

    static APIResponse<T> success_response(T&& data) {
        APIResponse<T> response;
        response.success = true;
        response.data = std::forward<T>(data);
        return response;
    }

    static APIResponse<T> error_response(const std::string& error) {
        APIResponse<T> response;
        response.success = false;
        response.message = error;
        return response;
    }
};

// Simplified DataHub API
class DataHubAPI {
public:
    explicit DataHubAPI(std::shared_ptr<Services::IDataHubManager> manager);

    // Quote data API
    APIResponse<Core::QuoteData> get_quote(const std::string& symbol);
    APIResponse<std::vector<Core::QuoteData>> get_quotes(const std::vector<std::string>& symbols);

    // Historical data API
    APIResponse<std::vector<Core::BarData>> get_bars(
        const std::string& symbol,
        const std::string& bar_size,
        const std::string& start_time,
        const std::string& end_time);

    // Security information API
    APIResponse<Core::SecurityInfo> get_security(const std::string& symbol);
    APIResponse<std::vector<Core::SecurityInfo>> search_securities(const std::string& query);

    // System API
    APIResponse<bool> health_check();

private:
    std::shared_ptr<Services::IDataHubManager> manager_;
};

} // namespace DataHub::API
