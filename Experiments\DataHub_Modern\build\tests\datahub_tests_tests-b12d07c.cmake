add_test( [==[Types - Enum conversions]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[Types - Enum conversions]==]  )
set_tests_properties( [==[Types - Enum conversions]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[Types - Result class]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[Types - Result class]==]  )
set_tests_properties( [==[Types - Result class]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[Types - Utility functions]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[Types - Utility functions]==]  )
set_tests_properties( [==[Types - Utility functions]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[MarketData - QuoteData]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[MarketData - QuoteData]==]  )
set_tests_properties( [==[MarketData - QuoteData]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[MarketData - BarData]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[MarketData - BarData]==]  )
set_tests_properties( [==[MarketData - BarData]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[MarketData - TickData]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[MarketData - TickData]==]  )
set_tests_properties( [==[MarketData - TickData]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[MarketData - Utility functions]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[MarketData - Utility functions]==]  )
set_tests_properties( [==[MarketData - Utility functions]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[MarketData - Advanced tests]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[MarketData - Advanced tests]==]  )
set_tests_properties( [==[MarketData - Advanced tests]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[SecurityInfo - Basic functionality]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[SecurityInfo - Basic functionality]==]  )
set_tests_properties( [==[SecurityInfo - Basic functionality]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
add_test( [==[BlockInfo - Block management]==] E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests/Release/datahub_tests.exe [==[BlockInfo - Block management]==]  )
set_tests_properties( [==[BlockInfo - Block management]==] PROPERTIES WORKING_DIRECTORY E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/tests)
set( datahub_tests_TESTS [==[Types - Enum conversions]==] [==[Types - Result class]==] [==[Types - Utility functions]==] [==[MarketData - QuoteData]==] [==[MarketData - BarData]==] [==[MarketData - TickData]==] [==[MarketData - Utility functions]==] [==[MarketData - Advanced tests]==] [==[SecurityInfo - Basic functionality]==] [==[BlockInfo - Block management]==])
