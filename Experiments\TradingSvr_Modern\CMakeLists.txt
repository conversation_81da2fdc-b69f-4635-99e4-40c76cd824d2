cmake_minimum_required(VERSION 3.20)
project(TradingSvr_Modern VERSION 1.0.0 LANGUAGES CXX)

# Set C++20 standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set UTF-8 encoding
if(MSVC)
    add_compile_options(/utf-8)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
endif()

# Build configuration
set(CMAKE_CONFIGURATION_TYPES "Debug;Release" CACHE STRING "" FORCE)

# Output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Find packages
find_package(Threads REQUIRED)
find_package(Boost REQUIRED COMPONENTS system thread filesystem)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../DataHub_Modern/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
)

# Source files
file(GLOB_RECURSE SOURCES
    "src/*.cpp"
    "src/*.cc"
)

# Remove test files from sources
list(FILTER SOURCES EXCLUDE REGEX ".*test.*")

file(GLOB_RECURSE HEADERS
    "include/*.h"
    "include/*.hpp"
)

# Ensure all required source files are included
set(REQUIRED_SOURCES
    src/Position.cpp
    src/Portfolio.cpp
    src/Strategy.cpp
    src/OrderManager.cpp
    src/Events.cpp
    src/ExpressionEngine.cpp
    src/ModelManager.cpp
    src/DataProvider.cpp
    src/NetworkManager.cpp
    src/TradingServer.cpp
    src/Trading.cpp
    src/Config.cpp
    src/Logger.cpp
    src/strategies/FuturesStrategy.cpp
    src/strategies/StockStrategy.cpp
)

# Verify all required sources exist
foreach(REQUIRED_SOURCE ${REQUIRED_SOURCES})
    if(NOT EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/${REQUIRED_SOURCE})
        message(WARNING "Required source file not found: ${REQUIRED_SOURCE}")
    endif()
endforeach()

# Create library
add_library(${PROJECT_NAME} STATIC ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(${PROJECT_NAME}
    PRIVATE
        Threads::Threads
        Boost::system
        Boost::thread
        Boost::filesystem
)

# Compiler-specific options
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE
        /W4 /WX- /bigobj
        /permissive-
        /Zc:__cplusplus
        /MP
    )
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        WIN32_LEAN_AND_MEAN
        NOMINMAX
        _WIN32_WINNT=0x0A00
    )
else()
    target_compile_options(${PROJECT_NAME} PRIVATE
        -Wall -Wextra -Wpedantic
        -Wno-unused-parameter
    )
endif()

# Add examples
add_subdirectory(examples)

# Enable testing
enable_testing()
add_subdirectory(tests)

# Installation
install(TARGETS ${PROJECT_NAME}
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h" PATTERN "*.hpp"
)
