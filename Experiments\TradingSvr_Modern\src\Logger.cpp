/**
 * @file Logger.cpp
 * @brief Simple logging system implementation
 * <AUTHOR> Team
 * @date 2024
 */

#include <iostream>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <mutex>
#include <thread>
#include <filesystem>

namespace RoboQuant::Trading {

enum class LogLevel {
    Debug = 0,
    Info = 1,
    Warning = 2,
    Error = 3,
    Critical = 4
};

class Logger {
public:
    static Logger& instance() {
        static Logger instance;
        return instance;
    }
    
    void set_log_level(LogLevel level) {
        std::lock_guard<std::mutex> lock(mutex_);
        log_level_ = level;
    }
    
    void set_log_directory(const std::string& directory) {
        std::lock_guard<std::mutex> lock(mutex_);
        log_directory_ = directory;
        
        // Create directory if it doesn't exist
        std::filesystem::create_directories(directory);
        
        // Open new log file
        open_log_file();
    }
    
    void set_console_output(bool enable) {
        std::lock_guard<std::mutex> lock(mutex_);
        console_output_ = enable;
    }
    
    void set_file_output(bool enable) {
        std::lock_guard<std::mutex> lock(mutex_);
        file_output_ = enable;
        
        if (enable && !log_file_.is_open()) {
            open_log_file();
        } else if (!enable && log_file_.is_open()) {
            log_file_.close();
        }
    }
    
    void log(LogLevel level, const std::string& component, const std::string& message) {
        if (level < log_level_) {
            return;
        }
        
        std::lock_guard<std::mutex> lock(mutex_);
        
        std::string formatted_message = format_message(level, component, message);
        
        if (console_output_) {
            if (level >= LogLevel::Error) {
                std::cerr << formatted_message << std::endl;
            } else {
                std::cout << formatted_message << std::endl;
            }
        }
        
        if (file_output_ && log_file_.is_open()) {
            log_file_ << formatted_message << std::endl;
            log_file_.flush();
        }
    }
    
    void debug(const std::string& component, const std::string& message) {
        log(LogLevel::Debug, component, message);
    }
    
    void info(const std::string& component, const std::string& message) {
        log(LogLevel::Info, component, message);
    }
    
    void warning(const std::string& component, const std::string& message) {
        log(LogLevel::Warning, component, message);
    }
    
    void error(const std::string& component, const std::string& message) {
        log(LogLevel::Error, component, message);
    }
    
    void critical(const std::string& component, const std::string& message) {
        log(LogLevel::Critical, component, message);
    }

private:
    Logger() = default;
    ~Logger() {
        if (log_file_.is_open()) {
            log_file_.close();
        }
    }
    
    std::string format_message(LogLevel level, const std::string& component, const std::string& message) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;
        
        std::ostringstream oss;
        oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        oss << "." << std::setfill('0') << std::setw(3) << ms.count();
        oss << " [" << level_to_string(level) << "]";
        oss << " [" << component << "]";
        oss << " [" << std::this_thread::get_id() << "]";
        oss << " " << message;
        
        return oss.str();
    }
    
    std::string level_to_string(LogLevel level) {
        switch (level) {
            case LogLevel::Debug: return "DEBUG";
            case LogLevel::Info: return "INFO ";
            case LogLevel::Warning: return "WARN ";
            case LogLevel::Error: return "ERROR";
            case LogLevel::Critical: return "CRIT ";
            default: return "UNKN ";
        }
    }
    
    void open_log_file() {
        if (!log_directory_.empty()) {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            
            std::ostringstream filename;
            filename << log_directory_ << "/trading_server_";
            filename << std::put_time(std::localtime(&time_t), "%Y%m%d");
            filename << ".log";
            
            if (log_file_.is_open()) {
                log_file_.close();
            }
            
            log_file_.open(filename.str(), std::ios::app);
        }
    }
    
    std::mutex mutex_;
    LogLevel log_level_ = LogLevel::Info;
    std::string log_directory_ = "./logs";
    bool console_output_ = true;
    bool file_output_ = true;
    std::ofstream log_file_;
};

// Global logging functions
void log_debug(const std::string& component, const std::string& message) {
    Logger::instance().debug(component, message);
}

void log_info(const std::string& component, const std::string& message) {
    Logger::instance().info(component, message);
}

void log_warning(const std::string& component, const std::string& message) {
    Logger::instance().warning(component, message);
}

void log_error(const std::string& component, const std::string& message) {
    Logger::instance().error(component, message);
}

void log_critical(const std::string& component, const std::string& message) {
    Logger::instance().critical(component, message);
}

void setup_logging(const std::string& log_level, const std::string& log_directory) {
    auto& logger = Logger::instance();
    
    // Set log level
    if (log_level == "debug") {
        logger.set_log_level(LogLevel::Debug);
    } else if (log_level == "info") {
        logger.set_log_level(LogLevel::Info);
    } else if (log_level == "warning") {
        logger.set_log_level(LogLevel::Warning);
    } else if (log_level == "error") {
        logger.set_log_level(LogLevel::Error);
    } else if (log_level == "critical") {
        logger.set_log_level(LogLevel::Critical);
    }
    
    // Set log directory
    logger.set_log_directory(log_directory);
    
    // Enable both console and file output
    logger.set_console_output(true);
    logger.set_file_output(true);
}

} // namespace RoboQuant::Trading
