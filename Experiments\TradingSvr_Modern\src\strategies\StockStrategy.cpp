/**
 * @file StockStrategy.cpp
 * @brief Implementation of StockStrategy class
 * <AUTHOR> Team
 * @date 2024
 */

#include "trading/strategies/StockStrategy.h"
#include "trading/Portfolio.h"
#include "trading/OrderManager.h"
#include "trading/DataProvider.h"
#include <algorithm>
#include <cmath>

namespace RoboQuant::Trading::Strategies {

StockStrategy::StockStrategy(StrategyConfig config) : Strategy(std::move(config)) {
    // Initialize stock-specific configuration from parameters
    stock_config_.long_entry_threshold = get_parameter<double>("long_entry_threshold").value_or(0.02);
    stock_config_.long_exit_threshold = get_parameter<double>("long_exit_threshold").value_or(0.01);
    stock_config_.stop_loss_pct = get_parameter<double>("stop_loss_pct").value_or(0.08);
    stock_config_.take_profit_pct = get_parameter<double>("take_profit_pct").value_or(0.15);
    stock_config_.max_position_value = get_parameter<double>("max_position_value").value_or(100000.0);
    stock_config_.max_portfolio_weight = get_parameter<double>("max_portfolio_weight").value_or(0.05);
    stock_config_.risk_per_trade = get_parameter<double>("risk_per_trade").value_or(0.01);
    
    // Initialize screening criteria
    stock_config_.min_volume = get_parameter<double>("min_volume").value_or(1000000.0);
    stock_config_.min_price = get_parameter<double>("min_price").value_or(5.0);
    stock_config_.max_price = get_parameter<double>("max_price").value_or(1000.0);
    stock_config_.min_market_cap = get_parameter<double>("min_market_cap").value_or(100000000.0);
    
    // Initialize sector and correlation limits
    stock_config_.enable_sector_limits = get_parameter<bool>("enable_sector_limits").value_or(true);
    stock_config_.max_sector_weight = get_parameter<double>("max_sector_weight").value_or(0.20);
    stock_config_.enable_correlation_limits = get_parameter<bool>("enable_correlation_limits").value_or(true);
    stock_config_.max_correlation = get_parameter<double>("max_correlation").value_or(0.7);
    
    // Parse avoided industries
    stock_config_.avoided_industries = {"Mining", "Utilities"}; // Default avoided industries
}

std::future<bool> StockStrategy::initialize() {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    try {
        log_info("Initializing StockStrategy: " + get_config().name);
        
        // Initialize universe screening
        if (!initialize_universe()) {
            log_error("Failed to initialize stock universe");
            promise.set_value(false);
            return future;
        }
        
        // Initialize factor models
        if (!initialize_models()) {
            log_error("Failed to initialize models");
            promise.set_value(false);
            return future;
        }
        
        // Setup timers
        setup_timers();
        
        promise.set_value(true);
        
    } catch (const std::exception& e) {
        log_error("Failed to initialize strategy: " + std::string(e.what()));
        promise.set_value(false);
    }
    
    return future;
}

std::future<void> StockStrategy::start() {
    std::promise<void> promise;
    auto future = promise.get_future();
    
    try {
        log_info("Starting StockStrategy");
        set_state(StrategyState::Running);
        
        // Start universe screening
        screen_universe();
        
        promise.set_value();
        
    } catch (const std::exception& e) {
        log_error("Failed to start strategy: " + std::string(e.what()));
        set_state(StrategyState::Error);
        promise.set_exception(std::current_exception());
    }
    
    return future;
}

std::future<void> StockStrategy::stop() {
    std::promise<void> promise;
    auto future = promise.get_future();
    
    try {
        log_info("Stopping StockStrategy");
        set_state(StrategyState::Stopped);
        promise.set_value();
        
    } catch (const std::exception& e) {
        log_error("Failed to stop strategy: " + std::string(e.what()));
        promise.set_exception(std::current_exception());
    }
    
    return future;
}

void StockStrategy::shutdown() {
    log_info("Shutting down StockStrategy");
    set_state(StrategyState::Stopped);
    
    // Clear all data
    std::unique_lock lock(strategy_data_mutex_);
    strategy_data_.clear();
    stock_universe_.clear();
    sector_exposures_.clear();
}

void StockStrategy::on_market_data(const QuoteData& quote) {
    if (!can_trade() || !is_asset_in_universe(quote.asset_id)) {
        return;
    }
    
    try {
        process_asset(quote.asset_id, quote);
    } catch (const std::exception& e) {
        log_error("Error processing market data for " + quote.asset_id + ": " + e.what());
    }
}

void StockStrategy::on_bar_data(const BarData& bar) {
    if (!can_trade() || !is_asset_in_universe(bar.asset_id)) {
        return;
    }
    
    try {
        process_asset(bar.asset_id, bar);
    } catch (const std::exception& e) {
        log_error("Error processing bar data for " + bar.asset_id + ": " + e.what());
    }
}

void StockStrategy::on_order_fill(const OrderFill& fill) {
    log_info("Order fill received: " + fill.order_id + " for " + fill.asset_id);
    
    // Update strategy data
    std::unique_lock lock(strategy_data_mutex_);
    auto it = strategy_data_.find(fill.asset_id);
    if (it != strategy_data_.end()) {
        auto& data = it->second;
        
        // Update performance metrics
        if (fill.side == Side::Sell && data.has_position()) {
            // Calculate realized P&L for closing trades
            if (data.entry_price.has_value()) {
                Amount pnl = static_cast<Amount>(fill.quantity) * (fill.price - *data.entry_price);
                data.realized_pnl += pnl;
                data.trade_count++;
            }
        }
        
        // Update sector exposure
        update_sector_exposure(fill.asset_id, fill.side, fill.quantity, fill.price);
    }
}

void StockStrategy::on_order_update(const Order& order) {
    log_info("Order update: " + order.id() + " status: " + std::to_string(static_cast<int>(order.status())));
}

void StockStrategy::on_timer(const std::string& timer_id) {
    if (timer_id == "universe_screening") {
        on_universe_screening_timer();
    } else if (timer_id == "rebalancing") {
        on_rebalancing_timer();
    } else if (timer_id == "risk_check") {
        on_risk_check_timer();
    } else if (timer_id == "earnings_check") {
        on_earnings_check_timer();
    }
}

void StockStrategy::set_stock_config(const StockStrategyConfig& config) {
    stock_config_ = config;
}

const StockStrategyConfig& StockStrategy::get_stock_config() const noexcept {
    return stock_config_;
}

std::optional<StockStrategyData> StockStrategy::get_strategy_data(const AssetId& asset_id) const {
    std::shared_lock lock(strategy_data_mutex_);
    auto it = strategy_data_.find(asset_id);
    return it != strategy_data_.end() ? std::make_optional(it->second) : std::nullopt;
}

std::vector<StockStrategyData> StockStrategy::get_all_strategy_data() const {
    std::shared_lock lock(strategy_data_mutex_);
    std::vector<StockStrategyData> result;
    result.reserve(strategy_data_.size());
    
    for (const auto& [asset_id, data] : strategy_data_) {
        result.push_back(data);
    }
    
    return result;
}

std::vector<AssetId> StockStrategy::get_stock_universe() const {
    std::shared_lock lock(universe_mutex_);
    return stock_universe_;
}

std::unordered_map<std::string, double> StockStrategy::get_sector_exposures() const {
    std::shared_lock lock(sector_mutex_);
    return sector_exposures_;
}

std::future<bool> StockStrategy::manual_open_position(const AssetId& asset_id, Quantity quantity, bool force) {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    if (!can_trade() && !force) {
        promise.set_value(false);
        return future;
    }
    
    try {
        auto result = open_long_position(asset_id, quantity);
        promise.set_value(result.get());
    } catch (const std::exception& e) {
        log_error("Failed to manually open position: " + std::string(e.what()));
        promise.set_value(false);
    }
    
    return future;
}

std::future<bool> StockStrategy::manual_close_position(const AssetId& asset_id) {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    try {
        auto result = close_position(asset_id);
        promise.set_value(result.get());
    } catch (const std::exception& e) {
        log_error("Failed to manually close position: " + std::string(e.what()));
        promise.set_value(false);
    }
    
    return future;
}

std::future<bool> StockStrategy::close_all_positions() {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    try {
        std::shared_lock lock(strategy_data_mutex_);
        std::vector<AssetId> assets_with_positions;
        
        for (const auto& [asset_id, data] : strategy_data_) {
            if (data.has_position()) {
                assets_with_positions.push_back(asset_id);
            }
        }
        lock.unlock();
        
        bool all_success = true;
        for (const auto& asset_id : assets_with_positions) {
            auto close_future = close_position(asset_id);
            if (!close_future.get()) {
                all_success = false;
            }
        }
        
        promise.set_value(all_success);
        
    } catch (const std::exception& e) {
        log_error("Failed to close all positions: " + std::string(e.what()));
        promise.set_value(false);
    }
    
    return future;
}

std::future<void> StockStrategy::rebalance_portfolio() {
    std::promise<void> promise;
    auto future = promise.get_future();
    
    try {
        log_info("Starting portfolio rebalancing");
        
        // Get current portfolio state
        auto current_positions = get_current_positions();
        
        // Generate new target portfolio
        auto target_portfolio = generate_target_portfolio();
        
        // Calculate trades needed
        auto trades = calculate_rebalancing_trades(current_positions, target_portfolio);
        
        // Execute trades
        execute_rebalancing_trades(trades);
        
        log_info("Portfolio rebalancing completed");
        promise.set_value();
        
    } catch (const std::exception& e) {
        log_error("Failed to rebalance portfolio: " + std::string(e.what()));
        promise.set_exception(std::current_exception());
    }
    
    return future;
}

// Private implementation methods
bool StockStrategy::initialize_universe() {
    try {
        // Initialize with a basic universe
        // In practice, this would load from configuration or screen from market data
        std::unique_lock lock(universe_mutex_);
        stock_universe_ = {"AAPL", "GOOGL", "MSFT", "AMZN", "TSLA", "META", "NVDA", "NFLX"};
        
        // Initialize strategy data for each asset
        std::unique_lock data_lock(strategy_data_mutex_);
        for (const auto& asset_id : stock_universe_) {
            initialize_strategy_data(asset_id);
        }
        
        return true;
        
    } catch (const std::exception& e) {
        log_error("Failed to initialize universe: " + std::string(e.what()));
        return false;
    }
}

bool StockStrategy::initialize_models() {
    try {
        // Initialize alpha and risk models
        // This would involve loading model configurations and connecting to model manager
        
        return true;
        
    } catch (const std::exception& e) {
        log_error("Failed to initialize models: " + std::string(e.what()));
        return false;
    }
}

void StockStrategy::setup_timers() {
    active_timers_.push_back("universe_screening");
    active_timers_.push_back("rebalancing");
    active_timers_.push_back("risk_check");
    active_timers_.push_back("earnings_check");
}

void StockStrategy::screen_universe() {
    // Perform universe screening based on fundamental criteria
    log_info("Performing universe screening");
    
    // This would involve querying fundamental data provider
    // and applying screening criteria
}

void StockStrategy::process_asset(const AssetId& asset_id, const QuoteData& quote) {
    // Update price cache
    {
        std::unique_lock lock(price_cache_mutex_);
        price_cache_[asset_id] = quote;
    }
    
    // Update strategy data
    update_strategy_data(asset_id, quote);
    
    // Generate signals and make trading decisions
    if (should_enter_long(asset_id)) {
        open_long_position(asset_id);
    }
    
    // Check exit conditions
    std::shared_lock lock(strategy_data_mutex_);
    auto it = strategy_data_.find(asset_id);
    if (it != strategy_data_.end() && it->second.has_position()) {
        if (should_exit_position(asset_id)) {
            close_position(asset_id);
        }
    }
}

void StockStrategy::process_asset(const AssetId& asset_id, const BarData& bar) {
    // Update strategy data with bar information
    update_strategy_data(asset_id, bar);
}

bool StockStrategy::should_enter_long(const AssetId& asset_id) const {
    double signal = generate_long_signal(asset_id);
    return signal > stock_config_.long_entry_threshold && 
           check_position_constraints(asset_id) &&
           check_sector_constraints(asset_id) &&
           check_correlation_constraints(asset_id);
}

bool StockStrategy::should_exit_position(const AssetId& asset_id) const {
    std::shared_lock lock(strategy_data_mutex_);
    auto it = strategy_data_.find(asset_id);
    if (it == strategy_data_.end() || !it->second.has_position()) {
        return false;
    }
    
    const auto& data = it->second;
    
    // Check holding time limits
    if (data.holding_time() > stock_config_.max_hold_time) {
        return true;
    }
    
    // Check exit signals
    double exit_signal = generate_exit_signal(asset_id);
    return exit_signal > stock_config_.long_exit_threshold;
}

double StockStrategy::generate_long_signal(const AssetId& asset_id) const {
    std::shared_lock lock(strategy_data_mutex_);
    auto it = strategy_data_.find(asset_id);
    if (it == strategy_data_.end()) {
        return 0.0;
    }
    
    const auto& data = it->second;
    
    // Multi-factor alpha signal
    double signal = data.alpha_score * 0.4 + 
                   data.momentum_score * 0.3 + 
                   data.quality_score * 0.2 + 
                   data.value_score * 0.1;
    
    // Apply confidence filter
    if (data.alpha_confidence < stock_config_.min_alpha_score) {
        signal *= 0.5;
    }
    
    return std::max(0.0, signal);
}

double StockStrategy::generate_exit_signal(const AssetId& asset_id) const {
    std::shared_lock lock(strategy_data_mutex_);
    auto it = strategy_data_.find(asset_id);
    if (it == strategy_data_.end()) {
        return 0.0;
    }
    
    const auto& data = it->second;
    
    // Exit signal based on alpha decay and risk factors
    double signal = -data.alpha_score * 0.6 + data.risk_score * 0.4;
    
    return std::max(0.0, signal);
}

std::future<bool> StockStrategy::open_long_position(const AssetId& asset_id, Quantity quantity) {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    try {
        Price current_price = get_current_price(asset_id);
        if (quantity == 0) {
            quantity = calculate_position_size(asset_id, current_price);
        }
        
        OrderRequest request;
        request.asset_id = asset_id;
        request.side = Side::Buy;
        request.type = stock_config_.use_limit_orders ? OrderType::Limit : OrderType::Market;
        request.quantity = quantity;
        request.strategy_id = get_config().id;
        
        if (stock_config_.use_limit_orders) {
            request.price = current_price * (1.0 + stock_config_.limit_order_offset);
        }
        
        // Check risk limits
        if (!check_position_risk(asset_id, request)) {
            promise.set_value(false);
            return future;
        }
        
        auto order_manager = get_order_manager();
        if (order_manager) {
            auto submit_future = order_manager->submit_order(request);
            auto order_id = submit_future.get();
            
            if (order_id.has_value()) {
                // Update strategy data
                std::unique_lock lock(strategy_data_mutex_);
                auto& data = strategy_data_[asset_id];
                data.entry_time = std::chrono::system_clock::now();
                data.entry_price = current_price;
                data.stop_loss_price = calculate_stop_loss_price(asset_id, current_price);
                data.take_profit_price = calculate_take_profit_price(asset_id, current_price);
                
                promise.set_value(true);
            } else {
                promise.set_value(false);
            }
        } else {
            promise.set_value(false);
        }
        
    } catch (const std::exception& e) {
        log_error("Failed to open long position: " + std::string(e.what()));
        promise.set_value(false);
    }
    
    return future;
}

std::future<bool> StockStrategy::close_position(const AssetId& asset_id) {
    std::promise<bool> promise;
    auto future = promise.get_future();
    
    // Implementation would close the position
    // For now, just return success
    promise.set_value(true);
    return future;
}

// Helper methods
void StockStrategy::initialize_strategy_data(const AssetId& asset_id) {
    auto& data = strategy_data_[asset_id];
    data.asset_id = asset_id;
    // Initialize other fields with default values
}

void StockStrategy::update_strategy_data(const AssetId& asset_id, const QuoteData& quote) {
    std::unique_lock lock(strategy_data_mutex_);
    auto& data = strategy_data_[asset_id];
    
    // Update basic price information and calculate factors
    // This is a placeholder implementation
    data.alpha_score = 0.5; // Placeholder
    data.momentum_score = 0.3; // Placeholder
    data.quality_score = 0.7; // Placeholder
    data.value_score = 0.4; // Placeholder
    data.risk_score = 0.2; // Placeholder
}

void StockStrategy::update_strategy_data(const AssetId& asset_id, const BarData& bar) {
    // Similar to quote update but with bar-specific calculations
    std::unique_lock lock(strategy_data_mutex_);
    auto& data = strategy_data_[asset_id];
    
    // Update with bar data and calculate technical indicators
}

bool StockStrategy::is_asset_in_universe(const AssetId& asset_id) const {
    std::shared_lock lock(universe_mutex_);
    return std::find(stock_universe_.begin(), stock_universe_.end(), asset_id) != stock_universe_.end();
}

Price StockStrategy::get_current_price(const AssetId& asset_id) const {
    std::shared_lock lock(price_cache_mutex_);
    auto it = price_cache_.find(asset_id);
    return it != price_cache_.end() ? it->second.last_price : 0.0;
}

Quantity StockStrategy::calculate_position_size(const AssetId& asset_id, Price price) const {
    // Calculate position size based on risk management rules
    Amount max_position_value = std::min(stock_config_.max_position_value,
                                        get_portfolio_value() * stock_config_.max_portfolio_weight);
    Amount risk_amount = max_position_value * stock_config_.risk_per_trade;
    return static_cast<Quantity>(risk_amount / price);
}

Price StockStrategy::calculate_stop_loss_price(const AssetId& asset_id, Price entry_price) const {
    return entry_price * (1.0 - stock_config_.stop_loss_pct);
}

Price StockStrategy::calculate_take_profit_price(const AssetId& asset_id, Price entry_price) const {
    return entry_price * (1.0 + stock_config_.take_profit_pct);
}

bool StockStrategy::check_position_risk(const AssetId& asset_id, const OrderRequest& request) const {
    Amount position_value = static_cast<Amount>(request.quantity) * 
                           (request.price.value_or(get_current_price(asset_id)));
    
    return position_value <= stock_config_.max_position_value;
}

bool StockStrategy::check_position_constraints(const AssetId& asset_id) const {
    // Check various position constraints like maximum number of positions
    return true; // Placeholder
}

bool StockStrategy::check_sector_constraints(const AssetId& asset_id) const {
    if (!stock_config_.enable_sector_limits) {
        return true;
    }
    
    // Check sector exposure limits
    std::shared_lock lock(sector_mutex_);
    // This would check if adding this position would exceed sector limits
    return true; // Placeholder
}

bool StockStrategy::check_correlation_constraints(const AssetId& asset_id) const {
    if (!stock_config_.enable_correlation_limits) {
        return true;
    }
    
    // Check correlation with existing positions
    return true; // Placeholder
}

Amount StockStrategy::get_portfolio_value() const {
    // Get total portfolio value
    auto portfolio = get_portfolio();
    if (portfolio) {
        auto price_provider = [this](const AssetId& asset_id) -> Price {
            return get_current_price(asset_id);
        };
        return portfolio->get_total_value(price_provider);
    }
    return 0.0;
}

void StockStrategy::update_sector_exposure(const AssetId& asset_id, Side side, Quantity quantity, Price price) {
    // Update sector exposure tracking
    std::unique_lock lock(sector_mutex_);
    // This would update sector exposures based on the trade
}

// Timer handlers
void StockStrategy::on_universe_screening_timer() {
    log_info("Performing scheduled universe screening");
    screen_universe();
}

void StockStrategy::on_rebalancing_timer() {
    log_info("Performing scheduled rebalancing");
    auto rebalance_future = rebalance_portfolio();
    // Don't wait for completion to avoid blocking
}

void StockStrategy::on_risk_check_timer() {
    log_info("Performing risk check");
    // Check portfolio risk metrics
}

void StockStrategy::on_earnings_check_timer() {
    log_info("Checking for upcoming earnings");
    // Check for upcoming earnings and adjust positions if needed
}

// Portfolio management methods
std::unordered_map<AssetId, Amount> StockStrategy::get_current_positions() const {
    std::unordered_map<AssetId, Amount> positions;
    
    std::shared_lock lock(strategy_data_mutex_);
    for (const auto& [asset_id, data] : strategy_data_) {
        if (data.has_position()) {
            Price current_price = get_current_price(asset_id);
            positions[asset_id] = static_cast<Amount>(data.position_quantity) * current_price;
        }
    }
    
    return positions;
}

std::unordered_map<AssetId, Amount> StockStrategy::generate_target_portfolio() const {
    std::unordered_map<AssetId, Amount> target_portfolio;
    
    // Generate target portfolio based on alpha scores
    std::shared_lock lock(strategy_data_mutex_);
    std::vector<std::pair<AssetId, double>> ranked_assets;
    
    for (const auto& [asset_id, data] : strategy_data_) {
        if (data.alpha_score > stock_config_.min_alpha_score) {
            ranked_assets.emplace_back(asset_id, data.alpha_score);
        }
    }
    
    // Sort by alpha score
    std::sort(ranked_assets.begin(), ranked_assets.end(),
              [](const auto& a, const auto& b) { return a.second > b.second; });
    
    // Allocate capital to top assets
    Amount total_capital = get_portfolio_value();
    size_t max_positions = std::min(ranked_assets.size(), static_cast<size_t>(50)); // Max 50 positions
    
    for (size_t i = 0; i < max_positions; ++i) {
        const auto& [asset_id, alpha_score] = ranked_assets[i];
        Amount allocation = total_capital * stock_config_.max_portfolio_weight * (alpha_score / 2.0);
        target_portfolio[asset_id] = allocation;
    }
    
    return target_portfolio;
}

std::vector<std::pair<AssetId, Amount>> StockStrategy::calculate_rebalancing_trades(
    const std::unordered_map<AssetId, Amount>& current_positions,
    const std::unordered_map<AssetId, Amount>& target_portfolio) const {
    
    std::vector<std::pair<AssetId, Amount>> trades;
    
    // Calculate trades needed to reach target portfolio
    std::set<AssetId> all_assets;
    for (const auto& [asset_id, value] : current_positions) {
        all_assets.insert(asset_id);
    }
    for (const auto& [asset_id, value] : target_portfolio) {
        all_assets.insert(asset_id);
    }
    
    for (const AssetId& asset_id : all_assets) {
        Amount current_value = current_positions.count(asset_id) ? current_positions.at(asset_id) : 0.0;
        Amount target_value = target_portfolio.count(asset_id) ? target_portfolio.at(asset_id) : 0.0;
        Amount trade_value = target_value - current_value;
        
        if (std::abs(trade_value) > 1000.0) { // Minimum trade threshold
            trades.emplace_back(asset_id, trade_value);
        }
    }
    
    return trades;
}

void StockStrategy::execute_rebalancing_trades(const std::vector<std::pair<AssetId, Amount>>& trades) {
    for (const auto& [asset_id, trade_value] : trades) {
        if (trade_value > 0) {
            // Buy
            Price current_price = get_current_price(asset_id);
            if (current_price > 0) {
                Quantity quantity = static_cast<Quantity>(trade_value / current_price);
                auto open_future = open_long_position(asset_id, quantity);
                // Don't wait for completion to avoid blocking
            }
        } else if (trade_value < 0) {
            // Sell
            auto close_future = close_position(asset_id);
            // Don't wait for completion to avoid blocking
        }
    }
}

// Factory Implementation
UniquePtr<Strategy> StockStrategyFactory::create_strategy(const StrategyConfig& config) {
    return std::make_unique<StockStrategy>(config);
}

std::vector<std::string> StockStrategyFactory::get_supported_strategies() const {
    return {"StockStrategy"};
}

} // namespace RoboQuant::Trading::Strategies
