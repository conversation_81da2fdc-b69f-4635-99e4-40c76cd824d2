/**
 * @file Logger.h
 * @brief Simple logging system declarations
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include <string>

namespace RoboQuant::Trading {

// Global logging functions
void log_debug(const std::string& component, const std::string& message);
void log_info(const std::string& component, const std::string& message);
void log_warning(const std::string& component, const std::string& message);
void log_error(const std::string& component, const std::string& message);
void log_critical(const std::string& component, const std::string& message);

// Setup function
void setup_logging(const std::string& log_level, const std::string& log_directory);

} // namespace RoboQuant::Trading
