/**
 * @file Config.cpp
 * @brief Implementation of configuration loading and saving functions
 * <AUTHOR> Team
 * @date 2024
 */

#include "trading/Trading.h"
#include <fstream>
#include <sstream>
#include <regex>
#include <iostream>

namespace RoboQuant::Trading::Config {

// Simple JSON parser implementation (for demonstration)
// In production, would use a proper JSON library like nlohmann/json

class SimpleJsonParser {
public:
    static std::unordered_map<std::string, std::string> parse_object(const std::string& json) {
        std::unordered_map<std::string, std::string> result;
        
        // Remove whitespace and braces
        std::string cleaned = json;
        cleaned.erase(std::remove_if(cleaned.begin(), cleaned.end(), ::isspace), cleaned.end());
        if (cleaned.front() == '{') cleaned.erase(0, 1);
        if (cleaned.back() == '}') cleaned.pop_back();
        
        // Split by commas
        std::regex pair_regex(R"("([^"]+)"\s*:\s*"([^"]*)")");
        std::regex number_regex(R"("([^"]+)"\s*:\s*([0-9.]+))");
        std::regex bool_regex(R"("([^"]+)"\s*:\s*(true|false))");
        
        std::sregex_iterator iter(json.begin(), json.end(), pair_regex);
        std::sregex_iterator end;
        
        for (; iter != end; ++iter) {
            std::smatch match = *iter;
            result[match[1].str()] = match[2].str();
        }
        
        // Parse numbers
        std::sregex_iterator num_iter(json.begin(), json.end(), number_regex);
        for (; num_iter != end; ++num_iter) {
            std::smatch match = *num_iter;
            result[match[1].str()] = match[2].str();
        }
        
        // Parse booleans
        std::sregex_iterator bool_iter(json.begin(), json.end(), bool_regex);
        for (; bool_iter != end; ++bool_iter) {
            std::smatch match = *bool_iter;
            result[match[1].str()] = match[2].str();
        }
        
        return result;
    }
    
    static std::string create_object(const std::unordered_map<std::string, std::string>& data) {
        std::ostringstream oss;
        oss << "{\n";
        
        bool first = true;
        for (const auto& [key, value] : data) {
            if (!first) oss << ",\n";
            
            // Try to determine if value is a number or boolean
            if (is_number(value) || value == "true" || value == "false") {
                oss << "  \"" << key << "\": " << value;
            } else {
                oss << "  \"" << key << "\": \"" << value << "\"";
            }
            first = false;
        }
        
        oss << "\n}";
        return oss.str();
    }
    
private:
    static bool is_number(const std::string& str) {
        std::regex number_regex(R"(^[0-9]+\.?[0-9]*$)");
        return std::regex_match(str, number_regex);
    }
};

// Server configuration
std::optional<TradingServerConfig> load_server_config(const std::string& config_file) {
    try {
        std::ifstream file(config_file);
        if (!file.is_open()) {
            std::cerr << "Failed to open config file: " << config_file << std::endl;
            return std::nullopt;
        }
        
        std::string json_content((std::istreambuf_iterator<char>(file)),
                                std::istreambuf_iterator<char>());
        file.close();
        
        auto data = SimpleJsonParser::parse_object(json_content);
        
        TradingServerConfig config;
        
        // Parse server configuration
        if (data.count("server_id")) config.server_id = data["server_id"];
        if (data.count("server_name")) config.server_name = data["server_name"];
        if (data.count("version")) config.version = data["version"];
        if (data.count("server_port")) config.server_port = static_cast<uint16_t>(std::stoi(data["server_port"]));
        if (data.count("network_threads")) config.network_threads = std::stoul(data["network_threads"]);
        if (data.count("enable_web_interface")) config.enable_web_interface = (data["enable_web_interface"] == "true");
        if (data.count("enable_api_server")) config.enable_api_server = (data["enable_api_server"] == "true");
        
        // Parse data provider configuration
        if (data.count("data_provider_type")) config.data_provider_type = data["data_provider_type"];
        if (data.count("data_cache_ttl_minutes")) {
            config.data_cache_ttl = std::chrono::minutes(std::stoi(data["data_cache_ttl_minutes"]));
        }
        
        // Parse model configuration
        if (data.count("model_directory")) config.model_directory = data["model_directory"];
        if (data.count("max_concurrent_predictions")) config.max_concurrent_predictions = std::stoul(data["max_concurrent_predictions"]);
        if (data.count("prediction_timeout_seconds")) {
            config.prediction_timeout = std::chrono::seconds(std::stoi(data["prediction_timeout_seconds"]));
        }
        
        // Parse strategy configuration
        if (data.count("strategy_config_file")) config.strategy_config_file = data["strategy_config_file"];
        if (data.count("auto_start_strategies")) config.auto_start_strategies = (data["auto_start_strategies"] == "true");
        if (data.count("strategy_heartbeat_interval_seconds")) {
            config.strategy_heartbeat_interval = std::chrono::seconds(std::stoi(data["strategy_heartbeat_interval_seconds"]));
        }
        
        // Parse risk configuration
        if (data.count("max_position_value")) config.global_risk_limits.max_position_value = std::stod(data["max_position_value"]);
        if (data.count("max_daily_loss")) config.global_risk_limits.max_daily_loss = std::stod(data["max_daily_loss"]);
        if (data.count("max_leverage")) config.global_risk_limits.max_leverage = std::stod(data["max_leverage"]);
        if (data.count("enable_risk_monitoring")) config.enable_risk_monitoring = (data["enable_risk_monitoring"] == "true");
        if (data.count("risk_check_interval_seconds")) {
            config.risk_check_interval = std::chrono::seconds(std::stoi(data["risk_check_interval_seconds"]));
        }
        
        // Parse logging configuration
        if (data.count("log_level")) config.log_level = data["log_level"];
        if (data.count("log_directory")) config.log_directory = data["log_directory"];
        if (data.count("enable_performance_monitoring")) config.enable_performance_monitoring = (data["enable_performance_monitoring"] == "true");
        if (data.count("performance_report_interval_minutes")) {
            config.performance_report_interval = std::chrono::minutes(std::stoi(data["performance_report_interval_minutes"]));
        }
        
        // Parse storage configuration
        if (data.count("data_directory")) config.data_directory = data["data_directory"];
        if (data.count("enable_auto_save")) config.enable_auto_save = (data["enable_auto_save"] == "true");
        if (data.count("auto_save_interval_minutes")) {
            config.auto_save_interval = std::chrono::minutes(std::stoi(data["auto_save_interval_minutes"]));
        }
        
        return config;
        
    } catch (const std::exception& e) {
        std::cerr << "Error loading server config: " << e.what() << std::endl;
        return std::nullopt;
    }
}

bool save_server_config(const TradingServerConfig& config, const std::string& config_file) {
    try {
        std::unordered_map<std::string, std::string> data;
        
        // Server configuration
        data["server_id"] = config.server_id;
        data["server_name"] = config.server_name;
        data["version"] = config.version;
        data["server_port"] = std::to_string(config.server_port);
        data["network_threads"] = std::to_string(config.network_threads);
        data["enable_web_interface"] = config.enable_web_interface ? "true" : "false";
        data["enable_api_server"] = config.enable_api_server ? "true" : "false";
        
        // Data provider configuration
        data["data_provider_type"] = config.data_provider_type;
        data["data_cache_ttl_minutes"] = std::to_string(config.data_cache_ttl.count());
        
        // Model configuration
        data["model_directory"] = config.model_directory;
        data["max_concurrent_predictions"] = std::to_string(config.max_concurrent_predictions);
        data["prediction_timeout_seconds"] = std::to_string(config.prediction_timeout.count());
        
        // Strategy configuration
        data["strategy_config_file"] = config.strategy_config_file;
        data["auto_start_strategies"] = config.auto_start_strategies ? "true" : "false";
        data["strategy_heartbeat_interval_seconds"] = std::to_string(config.strategy_heartbeat_interval.count());
        
        // Risk configuration
        data["max_position_value"] = std::to_string(config.global_risk_limits.max_position_value);
        data["max_daily_loss"] = std::to_string(config.global_risk_limits.max_daily_loss);
        data["max_leverage"] = std::to_string(config.global_risk_limits.max_leverage);
        data["enable_risk_monitoring"] = config.enable_risk_monitoring ? "true" : "false";
        data["risk_check_interval_seconds"] = std::to_string(config.risk_check_interval.count());
        
        // Logging configuration
        data["log_level"] = config.log_level;
        data["log_directory"] = config.log_directory;
        data["enable_performance_monitoring"] = config.enable_performance_monitoring ? "true" : "false";
        data["performance_report_interval_minutes"] = std::to_string(config.performance_report_interval.count());
        
        // Storage configuration
        data["data_directory"] = config.data_directory;
        data["enable_auto_save"] = config.enable_auto_save ? "true" : "false";
        data["auto_save_interval_minutes"] = std::to_string(config.auto_save_interval.count());
        
        std::string json_content = SimpleJsonParser::create_object(data);
        
        std::ofstream file(config_file);
        if (!file.is_open()) {
            std::cerr << "Failed to open config file for writing: " << config_file << std::endl;
            return false;
        }
        
        file << json_content;
        file.close();
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error saving server config: " << e.what() << std::endl;
        return false;
    }
}

// Strategy configuration
std::vector<StrategyConfig> load_strategy_configs(const std::string& config_file) {
    std::vector<StrategyConfig> configs;
    
    try {
        std::ifstream file(config_file);
        if (!file.is_open()) {
            std::cerr << "Failed to open strategy config file: " << config_file << std::endl;
            return configs;
        }
        
        std::string json_content((std::istreambuf_iterator<char>(file)),
                                std::istreambuf_iterator<char>());
        file.close();
        
        // Parse strategies array (simplified)
        std::regex strategy_regex(R"(\{[^}]+\})");
        std::sregex_iterator iter(json_content.begin(), json_content.end(), strategy_regex);
        std::sregex_iterator end;
        
        for (; iter != end; ++iter) {
            std::string strategy_json = iter->str();
            auto data = SimpleJsonParser::parse_object(strategy_json);
            
            StrategyConfig config;
            if (data.count("id")) config.id = data["id"];
            if (data.count("name")) config.name = data["name"];
            if (data.count("description")) config.description = data["description"];
            if (data.count("enabled")) config.enabled = (data["enabled"] == "true");
            
            // Parse parameters (simplified)
            for (const auto& [key, value] : data) {
                if (key != "id" && key != "name" && key != "description" && key != "enabled") {
                    // Try to parse as different types
                    if (value == "true" || value == "false") {
                        config.parameters[key] = (value == "true");
                    } else if (SimpleJsonParser::parse_object("").count(value) == 0) {
                        // Try to parse as number
                        try {
                            if (value.find('.') != std::string::npos) {
                                config.parameters[key] = std::stod(value);
                            } else {
                                config.parameters[key] = std::stoi(value);
                            }
                        } catch (...) {
                            config.parameters[key] = value; // String
                        }
                    } else {
                        config.parameters[key] = value; // String
                    }
                }
            }
            
            configs.push_back(config);
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Error loading strategy configs: " << e.what() << std::endl;
    }
    
    return configs;
}

bool save_strategy_configs(const std::vector<StrategyConfig>& configs, const std::string& config_file) {
    try {
        std::ostringstream oss;
        oss << "{\n  \"strategies\": [\n";
        
        for (size_t i = 0; i < configs.size(); ++i) {
            const auto& config = configs[i];
            
            if (i > 0) oss << ",\n";
            
            oss << "    {\n";
            oss << "      \"id\": \"" << config.id << "\",\n";
            oss << "      \"name\": \"" << config.name << "\",\n";
            oss << "      \"description\": \"" << config.description << "\",\n";
            oss << "      \"enabled\": " << (config.enabled ? "true" : "false");
            
            // Add parameters
            for (const auto& [key, value] : config.parameters) {
                oss << ",\n      \"" << key << "\": ";
                
                std::visit([&oss](const auto& v) {
                    using T = std::decay_t<decltype(v)>;
                    if constexpr (std::is_same_v<T, std::string>) {
                        oss << "\"" << v << "\"";
                    } else if constexpr (std::is_same_v<T, bool>) {
                        oss << (v ? "true" : "false");
                    } else {
                        oss << v;
                    }
                }, value);
            }
            
            oss << "\n    }";
        }
        
        oss << "\n  ]\n}";
        
        std::ofstream file(config_file);
        if (!file.is_open()) {
            std::cerr << "Failed to open strategy config file for writing: " << config_file << std::endl;
            return false;
        }
        
        file << oss.str();
        file.close();
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error saving strategy configs: " << e.what() << std::endl;
        return false;
    }
}

// Portfolio configuration
std::vector<PortfolioConfig> load_portfolio_configs(const std::string& config_file) {
    std::vector<PortfolioConfig> configs;
    
    try {
        std::ifstream file(config_file);
        if (!file.is_open()) {
            std::cerr << "Failed to open portfolio config file: " << config_file << std::endl;
            return configs;
        }
        
        std::string json_content((std::istreambuf_iterator<char>(file)),
                                std::istreambuf_iterator<char>());
        file.close();
        
        // Parse portfolios array (simplified)
        std::regex portfolio_regex(R"(\{[^}]+\})");
        std::sregex_iterator iter(json_content.begin(), json_content.end(), portfolio_regex);
        std::sregex_iterator end;
        
        for (; iter != end; ++iter) {
            std::string portfolio_json = iter->str();
            auto data = SimpleJsonParser::parse_object(portfolio_json);
            
            PortfolioConfig config;
            if (data.count("id")) config.id = data["id"];
            if (data.count("name")) config.name = data["name"];
            if (data.count("description")) config.description = data["description"];
            if (data.count("initial_capital")) config.initial_capital = std::stod(data["initial_capital"]);
            if (data.count("base_currency")) config.base_currency = data["base_currency"];
            if (data.count("enabled")) config.enabled = (data["enabled"] == "true");
            
            // Parse risk limits
            if (data.count("max_position_value")) config.risk_limits.max_position_value = std::stod(data["max_position_value"]);
            if (data.count("max_daily_loss")) config.risk_limits.max_daily_loss = std::stod(data["max_daily_loss"]);
            if (data.count("max_leverage")) config.risk_limits.max_leverage = std::stod(data["max_leverage"]);
            
            configs.push_back(config);
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Error loading portfolio configs: " << e.what() << std::endl;
    }
    
    return configs;
}

bool save_portfolio_configs(const std::vector<PortfolioConfig>& configs, const std::string& config_file) {
    try {
        std::ostringstream oss;
        oss << "{\n  \"portfolios\": [\n";
        
        for (size_t i = 0; i < configs.size(); ++i) {
            const auto& config = configs[i];
            
            if (i > 0) oss << ",\n";
            
            oss << "    {\n";
            oss << "      \"id\": \"" << config.id << "\",\n";
            oss << "      \"name\": \"" << config.name << "\",\n";
            oss << "      \"description\": \"" << config.description << "\",\n";
            oss << "      \"initial_capital\": " << config.initial_capital << ",\n";
            oss << "      \"base_currency\": \"" << config.base_currency << "\",\n";
            oss << "      \"enabled\": " << (config.enabled ? "true" : "false") << ",\n";
            oss << "      \"max_position_value\": " << config.risk_limits.max_position_value << ",\n";
            oss << "      \"max_daily_loss\": " << config.risk_limits.max_daily_loss << ",\n";
            oss << "      \"max_leverage\": " << config.risk_limits.max_leverage << "\n";
            oss << "    }";
        }
        
        oss << "\n  ]\n}";
        
        std::ofstream file(config_file);
        if (!file.is_open()) {
            std::cerr << "Failed to open portfolio config file for writing: " << config_file << std::endl;
            return false;
        }
        
        file << oss.str();
        file.close();
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error saving portfolio configs: " << e.what() << std::endl;
        return false;
    }
}

// Model configuration
std::vector<ModelConfig> load_model_configs(const std::string& config_file) {
    std::vector<ModelConfig> configs;
    
    try {
        std::ifstream file(config_file);
        if (!file.is_open()) {
            std::cerr << "Failed to open model config file: " << config_file << std::endl;
            return configs;
        }
        
        std::string json_content((std::istreambuf_iterator<char>(file)),
                                std::istreambuf_iterator<char>());
        file.close();
        
        // Parse models array (simplified)
        std::regex model_regex(R"(\{[^}]+\})");
        std::sregex_iterator iter(json_content.begin(), json_content.end(), model_regex);
        std::sregex_iterator end;
        
        for (; iter != end; ++iter) {
            std::string model_json = iter->str();
            auto data = SimpleJsonParser::parse_object(model_json);
            
            ModelConfig config;
            if (data.count("id")) config.id = data["id"];
            if (data.count("name")) config.name = data["name"];
            if (data.count("description")) config.description = data["description"];
            if (data.count("model_path")) config.model_path = data["model_path"];
            
            // Parse model type
            if (data.count("type")) {
                std::string type_str = data["type"];
                if (type_str == "LightGBM") config.type = ModelType::LightGBM;
                else if (type_str == "PyTorch") config.type = ModelType::PyTorch;
                else if (type_str == "ONNX") config.type = ModelType::ONNX;
                else if (type_str == "TensorFlow") config.type = ModelType::TensorFlow;
                else config.type = ModelType::Custom;
            }
            
            configs.push_back(config);
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Error loading model configs: " << e.what() << std::endl;
    }
    
    return configs;
}

bool save_model_configs(const std::vector<ModelConfig>& configs, const std::string& config_file) {
    try {
        std::ostringstream oss;
        oss << "{\n  \"models\": [\n";
        
        for (size_t i = 0; i < configs.size(); ++i) {
            const auto& config = configs[i];
            
            if (i > 0) oss << ",\n";
            
            oss << "    {\n";
            oss << "      \"id\": \"" << config.id << "\",\n";
            oss << "      \"name\": \"" << config.name << "\",\n";
            oss << "      \"description\": \"" << config.description << "\",\n";
            oss << "      \"model_path\": \"" << config.model_path << "\",\n";
            oss << "      \"type\": \"";
            
            switch (config.type) {
                case ModelType::LightGBM: oss << "LightGBM"; break;
                case ModelType::PyTorch: oss << "PyTorch"; break;
                case ModelType::ONNX: oss << "ONNX"; break;
                case ModelType::TensorFlow: oss << "TensorFlow"; break;
                default: oss << "Custom"; break;
            }
            
            oss << "\"\n    }";
        }
        
        oss << "\n  ]\n}";
        
        std::ofstream file(config_file);
        if (!file.is_open()) {
            std::cerr << "Failed to open model config file for writing: " << config_file << std::endl;
            return false;
        }
        
        file << oss.str();
        file.close();
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error saving model configs: " << e.what() << std::endl;
        return false;
    }
}

} // namespace RoboQuant::Trading::Config
